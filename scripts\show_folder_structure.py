import os

def create_folder_structure():
    """Display and create the complete folder structure"""
    
    structure = """
📁 resume-job-automation/
├── 📄 app.py                          # Main Flask application
├── 📄 requirements.txt                # Python dependencies
├── 📄 README.md                       # Project documentation
├── 📄 .gitignore                      # Git ignore file
├── 📄 .env.example                    # Environment variables template
│
├── 📁 templates/                      # Jinja2 HTML templates
│   ├── 📄 base.html                   # Base template with navbar/footer
│   ├── 📄 index.html                  # Home page (upload resume)
│   ├── 📄 edit_resume.html            # Edit resume data form
│   ├── 📄 confirm_automation.html     # Confirm before automation
│   └── 📄 jobs.html                   # Applied jobs dashboard
│
├── 📁 static/                         # Static files (CSS, JS, images)
│   ├── 📁 css/
│   │   └── 📄 custom.css              # Custom styles (optional)
│   ├── 📁 js/
│   │   └── 📄 app.js                  # Custom JavaScript (optional)
│   └── 📁 images/
│       └── 📄 logo.png                # App logo (optional)
│
├── 📁 uploads/                        # Uploaded resume files
│   └── 📄 .gitkeep                    # Keep folder in git
│
├── 📁 scripts/                        # Utility scripts
│   ├── 📄 setup_directories.py        # Create required directories
│   └── 📄 show_folder_structure.py    # This file
│
├── 📁 automation/                     # Browser automation modules
│   ├── 📄 __init__.py                 # Make it a Python package
│   ├── 📄 job_scraper.py              # Job scraping logic
│   └── 📄 form_filler.py              # Form filling automation
│
├── 📁 models/                         # Data models (if using database)
│   ├── 📄 __init__.py
│   ├── 📄 user.py                     # User data model
│   └── 📄 job.py                      # Job application model
│
├── 📁 utils/                          # Utility functions
│   ├── 📄 __init__.py
│   ├── 📄 file_handler.py             # File upload/processing
│   └── 📄 resume_parser.py            # Resume parsing logic
│
└── 📁 tests/                          # Unit tests
    ├── 📄 __init__.py
    ├── 📄 test_app.py                 # Test Flask routes
    └── 📄 test_automation.py          # Test automation functions
    """
    
    print("🏗️  RESUME JOB AUTOMATION - FOLDER STRUCTURE")
    print("=" * 50)
    print(structure)
    
    # Create the actual directories
    directories = [
        'templates',
        'static/css',
        'static/js', 
        'static/images',
        'uploads',
        'scripts',
        'automation',
        'models',
        'utils',
        'tests'
    ]
    
    print("\n🔧 Creating directories...")
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ Created: {directory}/")
        except Exception as e:
            print(f"❌ Error creating {directory}: {e}")
    
    # Create __init__.py files for Python packages
    init_files = [
        'automation/__init__.py',
        'models/__init__.py', 
        'utils/__init__.py',
        'tests/__init__.py'
    ]
    
    print("\n📝 Creating __init__.py files...")
    for init_file in init_files:
        try:
            with open(init_file, 'w') as f:
                f.write('# This file makes the directory a Python package\n')
            print(f"✅ Created: {init_file}")
        except Exception as e:
            print(f"❌ Error creating {init_file}: {e}")
    
    # Create .gitkeep for uploads directory
    try:
        with open('uploads/.gitkeep', 'w') as f:
            f.write('# This file keeps the uploads directory in git\n')
        print("✅ Created: uploads/.gitkeep")
    except Exception as e:
        print(f"❌ Error creating .gitkeep: {e}")
    
    print("\n🎉 Folder structure created successfully!")
    print("\n📋 Next steps:")
    print("1. Copy the Flask app files to their respective locations")
    print("2. Install dependencies: pip install -r requirements.txt")
    print("3. Run the app: python app.py")

if __name__ == "__main__":
    create_folder_structure()
