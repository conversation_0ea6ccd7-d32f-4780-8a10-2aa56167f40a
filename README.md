# Resume Job Automation

A Python Flask web application that automates job applications using resume data and browser automation.

## 🚀 Features

- **Resume Upload**: Upload and parse resume files (PDF, DOC, DOCX, TXT)
- **Data Editing**: Review and edit extracted resume information
- **Job Automation**: Automated job searching and application form filling
- **Application Tracking**: Dashboard to track applied jobs

## 📁 Project Structure

\`\`\`
resume-job-automation/
├── app.py                    # Main Flask application
├── requirements.txt          # Dependencies
├── templates/               # HTML templates
├── static/                  # CSS, JS, images
├── uploads/                 # Uploaded resumes
├── automation/              # Browser automation
├── models/                  # Data models
├── utils/                   # Utility functions
└── tests/                   # Unit tests
\`\`\`

## 🛠️ Setup

1. **Clone/Download** the project
2. **Install dependencies**:
   \`\`\`bash
   pip install -r requirements.txt
   \`\`\`
3. **Create directories**:
   \`\`\`bash
   python scripts/setup_directories.py
   \`\`\`
4. **Run the application**:
   \`\`\`bash
   python app.py
   \`\`\`
5. **Open browser**: http://localhost:5000

## 🔧 Configuration

- **Upload Directory**: `uploads/` (configurable in app.py)
- **Max File Size**: 16MB (configurable)
- **Supported Formats**: PDF, DOC, DOCX, TXT

## 🤖 Browser Automation

The app uses the `browser-use` library for job automation:
- Searches job boards (remoteok.com, etc.)
- Fills application forms automatically
- Does NOT submit applications (manual review required)

## 📝 Environment Variables

Create a `.env` file:
\`\`\`
FLASK_ENV=development
SECRET_KEY=your-secret-key-here
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216
\`\`\`

## 🧪 Testing

Run tests:
\`\`\`bash
python -m pytest tests/
\`\`\`

## 📄 License

MIT License - see LICENSE file for details.
