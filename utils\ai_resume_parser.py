"""
AI-Powered Resume Parser using Google Gemini
"""

import os
import re
import json
import logging
from typing import Dict, List, Optional, Any
import PyPDF2
import docx
from datetime import datetime
import google.generativeai as genai

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AIResumeParser:
    """AI-powered resume parser using Google Gemini"""
    
    def __init__(self):
        # Configure Gemini AI
        api_key = os.getenv('GOOGLE_API_KEY') or os.getenv('GEMINI_API_KEY')
        if api_key:
            genai.configure(api_key=api_key)
            self.model = genai.GenerativeModel('gemini-1.5-flash')
            self.ai_enabled = True
            print("✅ AI Resume Parser initialized with Gemini")
        else:
            self.ai_enabled = False
            print("⚠️ No Google API key found, using fallback parser")
        
        self.extraction_prompt = self._create_extraction_prompt()
    
    def _create_extraction_prompt(self) -> str:
        """Create comprehensive prompt for resume extraction"""
        return """
        You are an expert resume parser. Extract ALL possible information from the following resume text and return it as a JSON object with the following structure:

        {
            "personal_info": {
                "first_name": "string",
                "middle_name": "string",
                "last_name": "string",
                "full_name": "string",
                "email": "string",
                "phone_primary": "string",
                "phone_secondary": "string",
                "address_line1": "string",
                "address_line2": "string",
                "city": "string",
                "state_province": "string",
                "postal_code": "string",
                "country": "string",
                "date_of_birth": "YYYY-MM-DD or null",
                "nationality": "string",
                "linkedin_url": "string",
                "github_url": "string",
                "portfolio_url": "string",
                "personal_website": "string"
            },
            "professional_summary": "string - comprehensive summary",
            "career_objective": "string",
            "years_of_experience": "number",
            "current_position": "string",
            "current_company": "string",
            "skills": {
                "technical_skills": ["array of technical skills"],
                "programming_languages": ["array of programming languages"],
                "frameworks_libraries": ["array of frameworks and libraries"],
                "databases": ["array of databases"],
                "tools_software": ["array of tools and software"],
                "cloud_platforms": ["array of cloud platforms"],
                "operating_systems": ["array of operating systems"],
                "soft_skills": ["array of soft skills"],
                "methodologies": ["array of methodologies like Agile, Scrum"]
            },
            "work_experience": [
                {
                    "position": "string",
                    "company": "string",
                    "location": "string",
                    "start_date": "YYYY-MM or string",
                    "end_date": "YYYY-MM or 'Present' or string",
                    "duration": "string",
                    "description": "string",
                    "responsibilities": ["array of responsibilities"],
                    "achievements": ["array of achievements"],
                    "technologies_used": ["array of technologies"]
                }
            ],
            "education": [
                {
                    "degree": "string",
                    "field_of_study": "string",
                    "institution": "string",
                    "location": "string",
                    "graduation_date": "YYYY or string",
                    "gpa": "string or null",
                    "honors": "string or null",
                    "relevant_coursework": ["array of courses"]
                }
            ],
            "certifications": [
                {
                    "name": "string",
                    "issuing_organization": "string",
                    "issue_date": "YYYY-MM or string",
                    "expiry_date": "YYYY-MM or string or null",
                    "credential_id": "string or null",
                    "url": "string or null"
                }
            ],
            "projects": [
                {
                    "name": "string",
                    "description": "string",
                    "technologies": ["array of technologies used"],
                    "start_date": "YYYY-MM or string",
                    "end_date": "YYYY-MM or string or null",
                    "url": "string or null",
                    "github_url": "string or null",
                    "role": "string",
                    "key_features": ["array of key features"]
                }
            ],
            "languages_spoken": [
                {
                    "language": "string",
                    "proficiency": "Native/Fluent/Advanced/Intermediate/Basic"
                }
            ],
            "awards": [
                {
                    "name": "string",
                    "issuer": "string",
                    "date": "YYYY or string",
                    "description": "string"
                }
            ],
            "publications": [
                {
                    "title": "string",
                    "publication": "string",
                    "date": "YYYY-MM or string",
                    "url": "string or null",
                    "authors": ["array of authors"]
                }
            ],
            "volunteer_experience": [
                {
                    "organization": "string",
                    "role": "string",
                    "start_date": "YYYY-MM or string",
                    "end_date": "YYYY-MM or string or null",
                    "description": "string"
                }
            ],
            "professional_memberships": [
                {
                    "organization": "string",
                    "role": "string or null",
                    "start_date": "YYYY or string",
                    "end_date": "YYYY or string or null"
                }
            ],
            "references": [
                {
                    "name": "string",
                    "position": "string",
                    "company": "string",
                    "email": "string",
                    "phone": "string"
                }
            ],
            "additional_info": {
                "hobbies_interests": ["array of hobbies and interests"],
                "visa_status": "string or null",
                "willing_to_relocate": "boolean",
                "preferred_locations": ["array of preferred locations"],
                "salary_expectation": "string or null",
                "availability": "string or null"
            }
        }

        IMPORTANT INSTRUCTIONS:
        1. Extract ALL information present in the resume
        2. If information is not available, use null or empty array
        3. Be thorough and accurate
        4. Maintain proper JSON format
        5. For dates, try to standardize to YYYY-MM-DD format when possible
        6. Extract skills comprehensively and categorize them properly
        7. Include all work experience with detailed information
        8. Parse education information completely
        9. Identify and extract all projects mentioned
        10. Return ONLY the JSON object, no additional text

        Resume text to parse:
        """
    
    def parse_resume(self, file_path: str) -> Optional[Dict]:
        """Main parsing function using AI"""
        try:
            print(f"🤖 Starting AI resume parsing for: {file_path}")
            
            # Extract text from file
            text = self._extract_text(file_path)
            if not text:
                print("❌ Failed to extract text from resume")
                return None
            
            print(f"✅ Extracted {len(text)} characters from resume")
            
            # Use AI parsing if available, otherwise fallback
            if self.ai_enabled:
                parsed_data = self._ai_parse(text)
            else:
                parsed_data = self._fallback_parse(text)
            
            if parsed_data:
                parsed_data['parsing_method'] = 'ai' if self.ai_enabled else 'fallback'
                parsed_data['parsed_at'] = datetime.utcnow().isoformat()
                parsed_data['file_path'] = file_path
                parsed_data['text_length'] = len(text)
                
                print(f"✅ Resume parsing completed successfully using {'AI' if self.ai_enabled else 'fallback'} method")
                return parsed_data
            else:
                print("❌ Resume parsing failed")
                return None
                
        except Exception as e:
            print(f"❌ Error parsing resume: {e}")
            return None
    
    def _ai_parse(self, text: str) -> Optional[Dict]:
        """Parse resume using AI"""
        try:
            # Prepare prompt with resume text
            full_prompt = self.extraction_prompt + "\n\n" + text
            
            # Generate response using Gemini
            response = self.model.generate_content(full_prompt)
            
            if response and response.text:
                # Clean the response text
                response_text = response.text.strip()
                
                # Remove markdown code blocks if present
                if response_text.startswith('\`\`\`json'):
                    response_text = response_text[7:]
                if response_text.endswith('\`\`\`'):
                    response_text = response_text[:-3]
                
                response_text = response_text.strip()
                
                # Parse JSON
                parsed_data = json.loads(response_text)
                
                print("✅ AI parsing successful")
                return parsed_data
            else:
                print("❌ No response from AI model")
                return None
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing error: {e}")
            print(f"Response text: {response.text[:500]}...")
            return None
        except Exception as e:
            print(f"❌ AI parsing error: {e}")
            return None
    
    def _fallback_parse(self, text: str) -> Dict:
        """Fallback parsing method using regex and rules"""
        print("🔄 Using fallback parsing method")
        
        parsed_data = {
            "personal_info": self._extract_personal_info(text),
            "professional_summary": self._extract_summary(text),
            "years_of_experience": self._extract_years_experience(text),
            "skills": self._extract_skills(text),
            "work_experience": self._extract_work_experience(text),
            "education": self._extract_education(text),
            "projects": self._extract_projects(text),
            "certifications": self._extract_certifications(text),
            "languages_spoken": self._extract_languages(text),
            "additional_info": {
                "hobbies_interests": self._extract_hobbies(text),
                "preferred_locations": self._extract_locations(text)
            }
        }
        
        return parsed_data
    
    def _extract_text(self, file_path: str) -> str:
        """Extract text from different file formats"""
        try:
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext == '.pdf':
                return self._extract_from_pdf(file_path)
            elif file_ext in ['.doc', '.docx']:
                return self._extract_from_docx(file_path)
            elif file_ext == '.txt':
                return self._extract_from_txt(file_path)
            else:
                print(f"⚠️ Unsupported file format: {file_ext}")
                return ""
                
        except Exception as e:
            print(f"❌ Error extracting text: {e}")
            return ""
    
    def _extract_from_pdf(self, file_path: str) -> str:
        """Extract text from PDF"""
        try:
            text = ""
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
            return text
        except Exception as e:
            print(f"❌ Error reading PDF: {e}")
            return ""
    
    def _extract_from_docx(self, file_path: str) -> str:
        """Extract text from DOCX"""
        try:
            doc = docx.Document(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text
        except Exception as e:
            print(f"❌ Error reading DOCX: {e}")
            return ""
    
    def _extract_from_txt(self, file_path: str) -> str:
        """Extract text from TXT"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read()
        except Exception as e:
            print(f"❌ Error reading TXT: {e}")
            return ""
    
    # Fallback extraction methods
    def _extract_personal_info(self, text: str) -> Dict:
        """Extract personal information using regex"""
        personal_info = {}
        
        # Name extraction
        lines = text.split('\n')
        for line in lines[:5]:
            line = line.strip()
            if len(line) > 3 and len(line) < 50:
                name_match = re.match(r'^([A-Z][a-z]+ [A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)', line)
                if name_match:
                    full_name = name_match.group(1)
                    name_parts = full_name.split()
                    personal_info['full_name'] = full_name
                    personal_info['first_name'] = name_parts[0]
                    personal_info['last_name'] = name_parts[-1]
                    if len(name_parts) > 2:
                        personal_info['middle_name'] = ' '.join(name_parts[1:-1])
                    break
        
        # Email extraction
        email_match = re.search(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)
        if email_match:
            personal_info['email'] = email_match.group()
        
        # Phone extraction
        phone_match = re.search(r'(\+?[0-9\s\-$$$$]{10,15})', text)
        if phone_match:
            personal_info['phone_primary'] = phone_match.group().strip()
        
        # LinkedIn extraction
        linkedin_match = re.search(r'linkedin\.com/in/([A-Za-z0-9-]+)', text)
        if linkedin_match:
            personal_info['linkedin_url'] = f"https://linkedin.com/in/{linkedin_match.group(1)}"
        
        # GitHub extraction
        github_match = re.search(r'github\.com/([A-Za-z0-9-]+)', text)
        if github_match:
            personal_info['github_url'] = f"https://github.com/{github_match.group(1)}"
        
        return personal_info
    
    def _extract_summary(self, text: str) -> str:
        """Extract professional summary"""
        summary_patterns = [
            r'(?:professional\s+)?summary[:\s]+(.*?)(?:\n\n|\nexperience|\neducation|$)',
            r'objective[:\s]+(.*?)(?:\n\n|\nexperience|\neducation|$)',
            r'profile[:\s]+(.*?)(?:\n\n|\nexperience|\neducation|$)'
        ]
        
        for pattern in summary_patterns:
            match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if match:
                return match.group(1).strip()[:500]
        
        return ""
    
    def _extract_years_experience(self, text: str) -> Optional[int]:
        """Extract years of experience"""
        patterns = [
            r'(\d+)\+?\s+years?\s+(?:of\s+)?experience',
            r'(\d+)\+?\s+years?\s+in',
            r'experience[:\s]+(\d+)\+?\s+years?'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return int(match.group(1))
        
        return None
    
    def _extract_skills(self, text: str) -> Dict:
        """Extract skills and categorize them"""
        skills = {
            "technical_skills": [],
            "programming_languages": [],
            "frameworks_libraries": [],
            "databases": [],
            "tools_software": [],
            "cloud_platforms": [],
            "soft_skills": []
        }
        
        # Define skill categories
        programming_langs = ['Python', 'JavaScript', 'Java', 'C++', 'C#', 'PHP', 'Ruby', 'Go', 'Rust', 'Swift']
        frameworks = ['React', 'Angular', 'Vue', 'Django', 'Flask', 'Spring', 'Express', 'Laravel']
        databases = ['MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'SQLite', 'Oracle']
        cloud_platforms = ['AWS', 'Azure', 'Google Cloud', 'GCP', 'Heroku', 'DigitalOcean']
        
        # Extract from skills sections
        skills_section = re.search(r'skills?[:\s]+(.*?)(?:\n\n|\nexperience|\neducation|$)', text, re.IGNORECASE | re.DOTALL)
        if skills_section:
            skills_text = skills_section.group(1)
            skill_items = re.split(r'[,;•\n\t]+', skills_text)
            
            for skill in skill_items:
                skill = skill.strip()
                if skill:
                    skill_lower = skill.lower()
                    if any(lang.lower() in skill_lower for lang in programming_langs):
                        skills["programming_languages"].append(skill)
                    elif any(fw.lower() in skill_lower for fw in frameworks):
                        skills["frameworks_libraries"].append(skill)
                    elif any(db.lower() in skill_lower for db in databases):
                        skills["databases"].append(skill)
                    elif any(cloud.lower() in skill_lower for cloud in cloud_platforms):
                        skills["cloud_platforms"].append(skill)
                    else:
                        skills["technical_skills"].append(skill)
        
        return skills
    
    def _extract_work_experience(self, text: str) -> List[Dict]:
        """Extract work experience"""
        experience = []
        
        # Simple extraction - can be enhanced
        exp_section = re.search(r'experience[:\s]+(.*?)(?:\n\n|\neducation|\nprojects|$)', text, re.IGNORECASE | re.DOTALL)
        if exp_section:
            exp_text = exp_section.group(1)
            # This is a simplified extraction - AI parsing would be much better
            experience.append({
                "description": exp_text.strip()[:500],
                "position": "Not specified",
                "company": "Not specified"
            })
        
        return experience
    
    def _extract_education(self, text: str) -> List[Dict]:
        """Extract education information"""
        education = []
        
        edu_section = re.search(r'education[:\s]+(.*?)(?:\n\n|\nexperience|\nprojects|$)', text, re.IGNORECASE | re.DOTALL)
        if edu_section:
            edu_text = edu_section.group(1)
            education.append({
                "description": edu_text.strip()[:300],
                "degree": "Not specified",
                "institution": "Not specified"
            })
        
        return education
    
    def _extract_projects(self, text: str) -> List[Dict]:
        """Extract projects"""
        projects = []
        
        proj_section = re.search(r'projects?[:\s]+(.*?)(?:\n\n|\nexperience|\neducation|$)', text, re.IGNORECASE | re.DOTALL)
        if proj_section:
            proj_text = proj_section.group(1)
            projects.append({
                "description": proj_text.strip()[:300],
                "name": "Not specified"
            })
        
        return projects
    
    def _extract_certifications(self, text: str) -> List[Dict]:
        """Extract certifications"""
        certifications = []
        
        cert_section = re.search(r'certifications?[:\s]+(.*?)(?:\n\n|\nexperience|\neducation|$)', text, re.IGNORECASE | re.DOTALL)
        if cert_section:
            cert_text = cert_section.group(1)
            cert_items = re.split(r'[,;\n]+', cert_text)
            for cert in cert_items:
                cert = cert.strip()
                if cert:
                    certifications.append({
                        "name": cert,
                        "issuing_organization": "Not specified"
                    })
        
        return certifications
    
    def _extract_languages(self, text: str) -> List[Dict]:
        """Extract spoken languages"""
        languages = []
        common_languages = ['English', 'Spanish', 'French', 'German', 'Chinese', 'Japanese', 'Korean']
        
        for lang in common_languages:
            if re.search(r'\b' + lang + r'\b', text, re.IGNORECASE):
                languages.append({
                    "language": lang,
                    "proficiency": "Not specified"
                })
        
        return languages
    
    def _extract_hobbies(self, text: str) -> List[str]:
        """Extract hobbies and interests"""
        hobbies = []
        
        hobbies_section = re.search(r'(?:hobbies|interests)[:\s]+(.*?)(?:\n\n|\nexperience|\neducation|$)', text, re.IGNORECASE | re.DOTALL)
        if hobbies_section:
            hobbies_text = hobbies_section.group(1)
            hobby_items = re.split(r'[,;\n]+', hobbies_text)
            hobbies = [hobby.strip() for hobby in hobby_items if hobby.strip()]
        
        return hobbies
    
    def _extract_locations(self, text: str) -> List[str]:
        """Extract preferred locations"""
        locations = []
        
        # Look for location patterns
        location_matches = re.findall(r'([A-Z][a-z]+,\s*[A-Z]{2,})', text)
        for match in location_matches:
            if match not in locations:
                locations.append(match)
        
        return locations[:5]  # Limit to 5 locations

# Test function
def test_ai_resume_parser():
    """Test the AI resume parser"""
    parser = AIResumeParser()
    
    sample_text = """
    John Doe
    Senior Software Engineer
    <EMAIL> | (555) 123-4567 | San Francisco, CA
    linkedin.com/in/johndoe | github.com/johndoe
    
    PROFESSIONAL SUMMARY
    Experienced software engineer with 5+ years in full-stack development.
    Expertise in Python, JavaScript, and cloud technologies.
    
    TECHNICAL SKILLS
    Programming Languages: Python, JavaScript, Java, C++
    Web Technologies: React, Node.js, Django, Flask
    Databases: PostgreSQL, MongoDB, Redis
    Cloud: AWS, Docker, Kubernetes
    
    PROFESSIONAL EXPERIENCE
    Senior Software Engineer | TechCorp | 2020-Present
    - Developed scalable web applications using Python and React
    - Led team of 5 developers on microservices architecture
    
    EDUCATION
    Bachelor of Science in Computer Science
    University of California, Berkeley | 2018
    
    PROJECTS
    E-commerce Platform: Built full-stack application with React and Django
    """
    
    # Test AI parsing
    if parser.ai_enabled:
        result = parser._ai_parse(sample_text)
        print("✅ AI Parsing Test Results:")
        print(json.dumps(result, indent=2))
    else:
        print("⚠️ AI not available, testing fallback parser")
        result = parser._fallback_parse(sample_text)
        print("✅ Fallback Parsing Test Results:")
        print(json.dumps(result, indent=2))

if __name__ == "__main__":
    test_ai_resume_parser()
