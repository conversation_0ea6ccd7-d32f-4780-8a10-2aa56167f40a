# Legacy SQLAlchemy imports (for backward compatibility)
from .database import db, init_db

# Supabase imports (new)
from .supabase_config import get_supabase_client, init_supabase
from .supabase_database import init_supabase_db, get_db_stats as get_supabase_stats
from .supabase_auth import User, UserProfile
from .supabase_resume import Resume
from .supabase_job import Job
from .supabase_application import Application
from .supabase_website import JobWebsite
from .supabase_job_profile import JobProfile

# Legacy SQLAlchemy models (for backward compatibility)
try:
    from .auth import User as SQLUser, UserProfile as SQLUserProfile
    from .resume import Resume as SQLResume
    from .job import Job as SQLJob
    from .application import Application as SQLApplication
    from .website import JobWebsite as SQLJobWebsite
    from .job_profile import JobProfile as SQLJobProfile
except ImportError:
    # If SQLAlchemy models don't exist, use None
    SQLUser = SQLUserProfile = SQLResume = SQLJob = SQLApplication = SQLJobWebsite = SQLJobProfile = None

__all__ = [
    # Legacy SQLAlchemy
    'db', 'init_db',
    # Supabase
    'get_supabase_client', 'init_supabase', 'init_supabase_db', 'get_supabase_stats',
    # Models (Supabase versions)
    'User', 'UserProfile', 'Resume', 'Job', 'Application', 'JobWebsite', 'JobProfile',
    # Legacy models (for backward compatibility)
    'SQLUser', 'SQLUserProfile', 'SQLResume', 'SQLJob', 'SQLApplication', 'SQLJobWebsite', 'SQLJobProfile'
]
