"""
Utilities for migrating from SQLite to Supabase
"""
import sqlite3
import json
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging
import os

from models.supabase_config import get_supabase_client
from models.supabase_auth import User, UserProfile
from models.supabase_resume import Resume
from models.supabase_job import Job
from models.supabase_application import Application
from models.supabase_website import JobWebsite
from models.supabase_job_profile import JobProfile

logger = logging.getLogger(__name__)

class SupabaseMigration:
    """Handle migration from SQLite to Supabase"""
    
    def __init__(self, sqlite_db_path: str = None):
        if sqlite_db_path is None:
            # Default path
            sqlite_db_path = os.path.join(os.path.dirname(__file__), '..', 'instance', 'jobapp.db')
        
        self.sqlite_db_path = sqlite_db_path
        self.supabase_client = get_supabase_client()
        self.migration_log = []
    
    def log_migration(self, message: str, level: str = 'info'):
        """Log migration progress"""
        timestamp = datetime.utcnow().isoformat()
        log_entry = f"[{timestamp}] {level.upper()}: {message}"
        self.migration_log.append(log_entry)
        
        if level == 'error':
            logger.error(message)
        elif level == 'warning':
            logger.warning(message)
        else:
            logger.info(message)
        
        print(log_entry)
    
    def check_sqlite_exists(self) -> bool:
        """Check if SQLite database exists"""
        exists = os.path.exists(self.sqlite_db_path)
        if exists:
            self.log_migration(f"SQLite database found at: {self.sqlite_db_path}")
        else:
            self.log_migration(f"SQLite database not found at: {self.sqlite_db_path}", 'warning')
        return exists
    
    def get_sqlite_connection(self) -> Optional[sqlite3.Connection]:
        """Get SQLite database connection"""
        try:
            conn = sqlite3.connect(self.sqlite_db_path)
            conn.row_factory = sqlite3.Row  # Enable column access by name
            return conn
        except Exception as e:
            self.log_migration(f"Failed to connect to SQLite database: {e}", 'error')
            return None
    
    def create_supabase_tables(self) -> bool:
        """Create tables in Supabase (SQL commands to run in Supabase dashboard)"""
        
        sql_commands = [
            # Users table
            """
            CREATE TABLE IF NOT EXISTS users (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                email VARCHAR(255) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                full_name VARCHAR(255),
                phone VARCHAR(50),
                location VARCHAR(255),
                is_active BOOLEAN DEFAULT true,
                email_verified BOOLEAN DEFAULT false,
                verification_token VARCHAR(100),
                last_login TIMESTAMPTZ,
                login_count INTEGER DEFAULT 0,
                skills TEXT,
                experience TEXT,
                preferred_titles TEXT,
                preferred_locations TEXT,
                resume_parsed BOOLEAN DEFAULT false,
                automation_status VARCHAR(50) DEFAULT 'idle',
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW()
            );
            """,
            
            # User profiles table
            """
            CREATE TABLE IF NOT EXISTS user_profiles (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                skills TEXT,
                experience_summary TEXT,
                preferred_job_titles TEXT,
                preferred_locations TEXT,
                current_position VARCHAR(255),
                current_company VARCHAR(255),
                years_of_experience INTEGER,
                education_level VARCHAR(100),
                salary_expectation_min INTEGER,
                salary_expectation_max INTEGER,
                job_type_preference VARCHAR(50),
                remote_preference VARCHAR(50),
                linkedin_url VARCHAR(500),
                github_url VARCHAR(500),
                portfolio_url VARCHAR(500),
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW()
            );
            """,
            
            # Job websites table
            """
            CREATE TABLE IF NOT EXISTS job_websites (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                name VARCHAR(100) NOT NULL,
                url VARCHAR(500) NOT NULL,
                icon VARCHAR(50) DEFAULT 'globe',
                active BOOLEAN DEFAULT true,
                last_scraped TIMESTAMPTZ,
                scrape_count INTEGER DEFAULT 0,
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW()
            );
            """,
            
            # Resumes table
            """
            CREATE TABLE IF NOT EXISTS resumes (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                filename VARCHAR(255) NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                file_size INTEGER,
                file_type VARCHAR(50),
                extracted_data JSONB DEFAULT '{}',
                extraction_status VARCHAR(50) DEFAULT 'pending',
                extraction_error TEXT,
                parsed_data JSONB DEFAULT '{}',
                parsing_status VARCHAR(50) DEFAULT 'pending',
                uploaded_at TIMESTAMPTZ DEFAULT NOW(),
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW()
            );
            """,
            
            # Jobs table
            """
            CREATE TABLE IF NOT EXISTS jobs (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                website_id UUID REFERENCES job_websites(id) ON DELETE SET NULL,
                title VARCHAR(255) NOT NULL,
                company VARCHAR(255) NOT NULL,
                location VARCHAR(255),
                salary_range VARCHAR(100),
                job_url VARCHAR(500) NOT NULL,
                description TEXT,
                requirements TEXT,
                job_type VARCHAR(50),
                remote_type VARCHAR(50),
                external_id VARCHAR(255),
                tags JSONB DEFAULT '[]',
                found_at TIMESTAMPTZ DEFAULT NOW(),
                posted_at TIMESTAMPTZ,
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW()
            );
            """,
            
            # Applications table
            """
            CREATE TABLE IF NOT EXISTS applications (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                job_id UUID REFERENCES jobs(id) ON DELETE CASCADE,
                status VARCHAR(50) DEFAULT 'applied',
                cover_letter TEXT,
                notes TEXT,
                automation_log JSONB DEFAULT '[]',
                form_data JSONB DEFAULT '{}',
                applied_at TIMESTAMPTZ DEFAULT NOW(),
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW()
            );
            """
        ]
        
        self.log_migration("=== SUPABASE TABLE CREATION SQL ===")
        self.log_migration("Please run the following SQL commands in your Supabase dashboard:")
        self.log_migration("Go to: https://ngrnaolctyclsrvddgih.supabase.co/project/default/sql")
        self.log_migration("")
        
        for i, sql in enumerate(sql_commands, 1):
            self.log_migration(f"-- Command {i}:")
            self.log_migration(sql.strip())
            self.log_migration("")
        
        self.log_migration("=== END SQL COMMANDS ===")
        return True
    
    def migrate_users(self) -> bool:
        """Migrate users from SQLite to Supabase"""
        try:
            conn = self.get_sqlite_connection()
            if not conn:
                return False
            
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM users")
            users = cursor.fetchall()
            
            migrated_count = 0
            for user_row in users:
                try:
                    # Convert SQLite row to dict
                    user_data = dict(user_row)
                    
                    # Generate UUID for new system
                    user_data['id'] = str(uuid.uuid4())
                    
                    # Handle datetime fields
                    if user_data.get('created_at'):
                        user_data['created_at'] = user_data['created_at']
                    if user_data.get('updated_at'):
                        user_data['updated_at'] = user_data['updated_at']
                    if user_data.get('last_login'):
                        user_data['last_login'] = user_data['last_login']
                    
                    # Create user in Supabase
                    user = User.create(user_data)
                    if user:
                        migrated_count += 1
                        self.log_migration(f"Migrated user: {user_data.get('email', 'Unknown')}")
                    else:
                        self.log_migration(f"Failed to migrate user: {user_data.get('email', 'Unknown')}", 'error')
                
                except Exception as e:
                    self.log_migration(f"Error migrating user {user_row.get('email', 'Unknown')}: {e}", 'error')
            
            conn.close()
            self.log_migration(f"Successfully migrated {migrated_count} users")
            return True
            
        except Exception as e:
            self.log_migration(f"Error migrating users: {e}", 'error')
            return False

    def migrate_websites(self) -> bool:
        """Migrate job websites from SQLite to Supabase"""
        try:
            conn = self.get_sqlite_connection()
            if not conn:
                return False

            cursor = conn.cursor()
            cursor.execute("SELECT * FROM job_websites")
            websites = cursor.fetchall()

            migrated_count = 0
            for website_row in websites:
                try:
                    website_data = dict(website_row)
                    website_data['id'] = str(uuid.uuid4())

                    # Handle datetime fields
                    if website_data.get('created_at'):
                        website_data['created_at'] = website_data['created_at']
                    if website_data.get('updated_at'):
                        website_data['updated_at'] = website_data['updated_at']
                    if website_data.get('last_scraped'):
                        website_data['last_scraped'] = website_data['last_scraped']

                    website = JobWebsite.create(website_data)
                    if website:
                        migrated_count += 1
                        self.log_migration(f"Migrated website: {website_data.get('name', 'Unknown')}")
                    else:
                        self.log_migration(f"Failed to migrate website: {website_data.get('name', 'Unknown')}", 'error')

                except Exception as e:
                    self.log_migration(f"Error migrating website {website_row.get('name', 'Unknown')}: {e}", 'error')

            conn.close()
            self.log_migration(f"Successfully migrated {migrated_count} websites")
            return True

        except Exception as e:
            self.log_migration(f"Error migrating websites: {e}", 'error')
            return False

    def migrate_resumes(self) -> bool:
        """Migrate resumes from SQLite to Supabase"""
        try:
            conn = self.get_sqlite_connection()
            if not conn:
                return False

            cursor = conn.cursor()
            cursor.execute("SELECT * FROM resumes")
            resumes = cursor.fetchall()

            migrated_count = 0
            for resume_row in resumes:
                try:
                    resume_data = dict(resume_row)
                    resume_data['id'] = str(uuid.uuid4())

                    # Handle JSON fields
                    if resume_data.get('extracted_data'):
                        if isinstance(resume_data['extracted_data'], str):
                            try:
                                resume_data['extracted_data'] = json.loads(resume_data['extracted_data'])
                            except json.JSONDecodeError:
                                resume_data['extracted_data'] = {}

                    if resume_data.get('parsed_data'):
                        if isinstance(resume_data['parsed_data'], str):
                            try:
                                resume_data['parsed_data'] = json.loads(resume_data['parsed_data'])
                            except json.JSONDecodeError:
                                resume_data['parsed_data'] = {}

                    # Handle datetime fields
                    if resume_data.get('uploaded_at'):
                        resume_data['uploaded_at'] = resume_data['uploaded_at']
                    if resume_data.get('created_at'):
                        resume_data['created_at'] = resume_data['created_at']
                    if resume_data.get('updated_at'):
                        resume_data['updated_at'] = resume_data['updated_at']

                    resume = Resume.create(resume_data)
                    if resume:
                        migrated_count += 1
                        self.log_migration(f"Migrated resume: {resume_data.get('filename', 'Unknown')}")
                    else:
                        self.log_migration(f"Failed to migrate resume: {resume_data.get('filename', 'Unknown')}", 'error')

                except Exception as e:
                    self.log_migration(f"Error migrating resume {resume_row.get('filename', 'Unknown')}: {e}", 'error')

            conn.close()
            self.log_migration(f"Successfully migrated {migrated_count} resumes")
            return True

        except Exception as e:
            self.log_migration(f"Error migrating resumes: {e}", 'error')
            return False

    def run_full_migration(self) -> bool:
        """Run complete migration from SQLite to Supabase"""
        self.log_migration("=== STARTING SUPABASE MIGRATION ===")

        if not self.check_sqlite_exists():
            self.log_migration("No SQLite database found. Creating fresh Supabase setup.", 'warning')
            return self.create_supabase_tables()

        # Step 1: Create tables (show SQL commands)
        self.log_migration("Step 1: Creating Supabase tables...")
        if not self.create_supabase_tables():
            self.log_migration("Failed to create Supabase tables", 'error')
            return False

        self.log_migration("Please run the SQL commands above in Supabase dashboard, then press Enter to continue...")
        input()  # Wait for user to create tables

        # Step 2: Migrate users
        self.log_migration("Step 2: Migrating users...")
        if not self.migrate_users():
            self.log_migration("Failed to migrate users", 'error')
            return False

        # Step 3: Migrate websites
        self.log_migration("Step 3: Migrating job websites...")
        if not self.migrate_websites():
            self.log_migration("Failed to migrate websites", 'error')
            return False

        # Step 4: Migrate resumes
        self.log_migration("Step 4: Migrating resumes...")
        if not self.migrate_resumes():
            self.log_migration("Failed to migrate resumes", 'error')
            return False

        self.log_migration("=== MIGRATION COMPLETED SUCCESSFULLY ===")
        return True

    def save_migration_log(self, filename: str = None) -> str:
        """Save migration log to file"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"supabase_migration_{timestamp}.log"

        try:
            with open(filename, 'w') as f:
                f.write('\n'.join(self.migration_log))

            self.log_migration(f"Migration log saved to: {filename}")
            return filename

        except Exception as e:
            self.log_migration(f"Failed to save migration log: {e}", 'error')
            return ""
