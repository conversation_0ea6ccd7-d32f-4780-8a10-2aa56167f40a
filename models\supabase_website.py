"""
Supabase-compatible JobWebsite model
"""
from typing import Optional, Dict, Any, List
from datetime import datetime
import uuid
import logging
from .supabase_database import SupabaseModel

logger = logging.getLogger(__name__)

class JobWebsite(SupabaseModel):
    """Job website model for Supabase"""
    
    table_name = "job_websites"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Set default values
        self.id = kwargs.get('id', str(uuid.uuid4()))
        self.user_id = kwargs.get('user_id', '')
        
        # Website information
        self.name = kwargs.get('name', '')
        self.url = kwargs.get('url', '')
        self.icon = kwargs.get('icon', 'globe')
        
        # Status
        self.active = kwargs.get('active', True)
        self.last_scraped = kwargs.get('last_scraped')
        self.scrape_count = kwargs.get('scrape_count', 0)
        
        # Timestamps
        self.created_at = kwargs.get('created_at', datetime.utcnow().isoformat())
        self.updated_at = kwargs.get('updated_at', datetime.utcnow().isoformat())
    
    @classmethod
    def create_website(cls, user_id: str, name: str, url: str, 
                      icon: str = 'globe', active: bool = True) -> Optional['JobWebsite']:
        """Create a new job website record"""
        try:
            website_data = {
                'id': str(uuid.uuid4()),
                'user_id': user_id,
                'name': name,
                'url': url,
                'icon': icon,
                'active': active,
                'scrape_count': 0
            }
            
            return cls.create(website_data)
            
        except Exception as e:
            logger.error(f"Error creating job website: {e}")
            return None
    
    @classmethod
    def create_defaults_for_user(cls, user_id: str) -> List['JobWebsite']:
        """Create default websites for user"""
        defaults = [
            {'name': 'RemoteOK', 'url': 'https://remoteok.io', 'icon': 'globe'},
            {'name': 'We Work Remotely', 'url': 'https://weworkremotely.com', 'icon': 'briefcase'},
            {'name': 'AngelList', 'url': 'https://angel.co', 'icon': 'trending-up'}
        ]
        
        websites = []
        for default in defaults:
            website = cls.create_website(
                user_id=user_id,
                name=default['name'],
                url=default['url'],
                icon=default['icon'],
                active=True
            )
            if website:
                websites.append(website)
        
        return websites
    
    def update_scrape_info(self, increment_count: bool = True) -> bool:
        """Update scraping information"""
        try:
            update_data = {
                'last_scraped': datetime.utcnow().isoformat()
            }
            
            if increment_count:
                update_data['scrape_count'] = (self.scrape_count or 0) + 1
            
            return self.update(update_data)
            
        except Exception as e:
            logger.error(f"Error updating scrape info: {e}")
            return False
    
    def toggle_active(self) -> bool:
        """Toggle website active status"""
        try:
            return self.update({'active': not self.active})
        except Exception as e:
            logger.error(f"Error toggling website active status: {e}")
            return False
    
    def get_user(self):
        """Get the user who owns this website"""
        from .supabase_auth import User
        return User.get_by_id(self.user_id)
    
    def get_jobs(self):
        """Get jobs from this website"""
        from .supabase_job import Job
        return Job.filter_by(website_id=self.id)
    
    def get_job_count(self) -> int:
        """Get count of jobs from this website"""
        try:
            client = self.get_client()
            result = client.table('jobs').select('id', count='exact').eq('website_id', self.id).execute()
            return result.count if result.count is not None else 0
        except Exception as e:
            logger.error(f"Error getting job count: {e}")
            return 0
    
    @classmethod
    def get_by_user(cls, user_id: str) -> List['JobWebsite']:
        """Get all websites for a user"""
        return cls.filter_by(user_id=user_id)
    
    @classmethod
    def get_active_by_user(cls, user_id: str) -> List['JobWebsite']:
        """Get active websites for a user"""
        try:
            client = cls.get_client()
            result = client.table(cls.table_name).select("*").eq('user_id', user_id).eq('active', True).execute()
            
            return [cls(**record) for record in result.data]
            
        except Exception as e:
            logger.error(f"Error getting active websites: {e}")
            return []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert website to dictionary"""
        result = super().to_dict()
        
        # Add computed fields
        result['job_count'] = self.get_job_count()
        
        return result
    
    def get_display_name(self) -> str:
        """Get display name for website"""
        return self.name or self.url
    
    def is_recently_scraped(self, hours: int = 24) -> bool:
        """Check if website was scraped recently"""
        if not self.last_scraped:
            return False
        
        try:
            if isinstance(self.last_scraped, str):
                last_scraped = datetime.fromisoformat(self.last_scraped.replace('Z', '+00:00'))
            else:
                last_scraped = self.last_scraped
            
            time_diff = datetime.utcnow() - last_scraped.replace(tzinfo=None)
            return time_diff.total_seconds() < (hours * 3600)
            
        except Exception as e:
            logger.error(f"Error checking recent scrape: {e}")
            return False
