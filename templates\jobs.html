{% extends "base.html" %}

{% block content %}
<div class="main-card">
    <div class="card-header">
        <h1 class="card-title">Applied Jobs</h1>
        <p class="card-subtitle">Track your automated job applications and their status</p>
    </div>

    {% if jobs %}
    <div style="margin-bottom: 32px;">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 32px;">
            <div style="background: #eff6ff; padding: 20px; border-radius: 12px; text-align: center;">
                <h3 style="font-size: 32px; font-weight: 700; color: #1d4ed8; margin-bottom: 8px;">{{ jobs|length }}</h3>
                <p style="color: #6b7280; font-size: 14px;">Total Applications</p>
            </div>
            <div style="background: #f0fdf4; padding: 20px; border-radius: 12px; text-align: center;">
                <h3 style="font-size: 32px; font-weight: 700; color: #059669; margin-bottom: 8px;">{{ jobs|selectattr("status", "equalto", "Applied")|list|length }}</h3>
                <p style="color: #6b7280; font-size: 14px;">Successfully Applied</p>
            </div>
            <div style="background: #fef3c7; padding: 20px; border-radius: 12px; text-align: center;">
                <h3 style="font-size: 32px; font-weight: 700; color: #d97706; margin-bottom: 8px;">{{ jobs|map(attribute='company')|unique|list|length }}</h3>
                <p style="color: #6b7280; font-size: 14px;">Unique Companies</p>
            </div>
            <div style="background: #f3e8ff; padding: 20px; border-radius: 12px; text-align: center;">
                <h3 style="font-size: 32px; font-weight: 700; color: #7c3aed; margin-bottom: 8px;">Today</h3>
                <p style="color: #6b7280; font-size: 14px;">Last Run</p>
            </div>
        </div>

        <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: #f9fafb; border-bottom: 1px solid #e5e7eb;">
                        <th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Job Title</th>
                        <th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Company</th>
                        <th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Website</th>
                        <th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Date Applied</th>
                        <th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Status</th>
                        <th style="padding: 16px; text-align: left; font-weight: 600; color: #374151;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for job in jobs %}
                    <tr style="border-bottom: 1px solid #f3f4f6;">
                        <td style="padding: 16px;">
                            <div style="font-weight: 600; color: #374151;">{{ job.title }}</div>
                        </td>
                        <td style="padding: 16px; color: #6b7280;">{{ job.company }}</td>
                        <td style="padding: 16px; color: #6b7280;">{{ job.website if job.website else 'N/A' }}</td>
                        <td style="padding: 16px; color: #6b7280;">{{ job.date }}</td>
                        <td style="padding: 16px;">
                            <span style="background: #d1fae5; color: #065f46; padding: 4px 12px; border-radius: 16px; font-size: 12px; font-weight: 600;">
                                {{ job.status }}
                            </span>
                        </td>
                        <td style="padding: 16px;">
                            <a href="{{ job.link }}" target="_blank" class="btn btn-secondary" style="padding: 8px 16px; font-size: 14px;">
                                <i class="fas fa-external-link-alt"></i>
                                View Job
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% else %}
    <div style="text-align: center; padding: 60px 20px;">
        <i class="fas fa-briefcase" style="font-size: 64px; color: #d1d5db; margin-bottom: 24px;"></i>
        <h3 style="font-size: 20px; font-weight: 600; color: #374151; margin-bottom: 12px;">No jobs applied yet</h3>
        <p style="color: #6b7280; margin-bottom: 32px;">Start by uploading your resume and running the automation process.</p>
        <a href="{{ url_for('upload') }}" class="btn btn-primary">
            <i class="fas fa-upload"></i>
            Upload Resume
        </a>
    </div>
    {% endif %}

    <div style="text-align: center; margin-top: 32px; padding-top: 32px; border-top: 1px solid #e5e7eb;">
        <a href="{{ url_for('automate') }}" class="btn btn-primary">
            <i class="fas fa-redo"></i>
            Run Automation Again
        </a>
    </div>
</div>
{% endblock %}
