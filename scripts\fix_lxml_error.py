import subprocess
import sys

def fix_lxml_installation():
    """Fix the lxml.html.clean import error"""
    
    print("🔧 Fixing lxml.html.clean ImportError")
    print("=" * 40)
    
    # Commands to run
    commands = [
        "pip uninstall -y lxml",
        "pip uninstall -y lxml_html_clean", 
        "pip install lxml[html_clean]==5.1.0",
        "pip install lxml_html_clean==0.1.1",
        "pip install beautifulsoup4==4.12.3"
    ]
    
    print("Running fix commands...")
    
    for i, cmd in enumerate(commands, 1):
        print(f"\n{i}. {cmd}")
        try:
            result = subprocess.run(cmd.split(), capture_output=True, text=True)
            if result.returncode == 0:
                print("   ✅ Success")
            else:
                print(f"   ⚠️  Warning: {result.stderr.strip()}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n🧪 Testing import...")
    try:
        import lxml.html.clean
        print("✅ lxml.html.clean import successful!")
    except ImportError as e:
        print(f"❌ Still having issues: {e}")
        print("\n🔄 Alternative solution:")
        print("Try running these commands manually:")
        print("pip install --upgrade lxml")
        print("pip install lxml_html_clean")
        print("pip install 'lxml[html_clean]'")
    
    print("\n📋 Alternative: Use BeautifulSoup instead")
    print("If lxml continues to cause issues, we can switch to BeautifulSoup")
    print("for HTML parsing, which is more stable.")

if __name__ == "__main__":
    fix_lxml_installation()
