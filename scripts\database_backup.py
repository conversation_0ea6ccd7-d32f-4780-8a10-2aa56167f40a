"""
Database backup and restore utilities
"""

import os
import shutil
import sqlite3
from datetime import datetime
import json

def backup_database():
    """Create a backup of the database"""
    
    db_path = os.path.join('instance', 'jobapp.db')
    if not os.path.exists(db_path):
        print("❌ Database file not found!")
        return False
    
    # Create backups directory
    backup_dir = 'backups'
    os.makedirs(backup_dir, exist_ok=True)
    
    # Create backup filename with timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_filename = f'jobapp_backup_{timestamp}.db'
    backup_path = os.path.join(backup_dir, backup_filename)
    
    try:
        # Copy database file
        shutil.copy2(db_path, backup_path)
        
        # Export data as JSON for additional backup
        json_backup_path = os.path.join(backup_dir, f'jobapp_data_{timestamp}.json')
        export_data_to_json(db_path, json_backup_path)
        
        print(f"✅ Database backed up successfully!")
        print(f"📁 SQLite backup: {backup_path}")
        print(f"📄 JSON backup: {json_backup_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Backup failed: {e}")
        return False

def export_data_to_json(db_path, json_path):
    """Export database data to JSON"""
    
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row  # Enable column access by name
    
    data = {}
    
    # Get all table names
    cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = [row[0] for row in cursor.fetchall()]
    
    # Export each table
    for table in tables:
        cursor = conn.execute(f"SELECT * FROM {table}")
        rows = cursor.fetchall()
        data[table] = [dict(row) for row in rows]
    
    conn.close()
    
    # Write to JSON file
    with open(json_path, 'w') as f:
        json.dump(data, f, indent=2, default=str)

def list_backups():
    """List available backups"""
    
    backup_dir = 'backups'
    if not os.path.exists(backup_dir):
        print("📁 No backups directory found")
        return []
    
    backups = []
    for filename in os.listdir(backup_dir):
        if filename.startswith('jobapp_backup_') and filename.endswith('.db'):
            filepath = os.path.join(backup_dir, filename)
            stat = os.stat(filepath)
            backups.append({
                'filename': filename,
                'path': filepath,
                'size': stat.st_size,
                'created': datetime.fromtimestamp(stat.st_ctime)
            })
    
    # Sort by creation time (newest first)
    backups.sort(key=lambda x: x['created'], reverse=True)
    
    print(f"📋 Available backups ({len(backups)}):")
    for backup in backups:
        size_mb = backup['size'] / (1024 * 1024)
        print(f"  - {backup['filename']} ({size_mb:.1f}MB) - {backup['created']}")
    
    return backups

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'list':
        list_backups()
    else:
        backup_database()
