{% extends "base.html" %}

{% block title %}Profile - AI Job Application Assistant{% endblock %}

{% block content %}
<div class="main-card">
    <div class="card-header">
        <h1 class="card-title">My Profile</h1>
        <p class="card-subtitle">Manage your account and job search preferences</p>
    </div>

    <!-- Account Information -->
    <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 24px;">
        <h3 style="font-size: 16px; font-weight: 600; color: #334155; margin-bottom: 12px;">Account Information</h3>
        <div class="grid-2" style="gap: 12px;">
            <div>
                <span style="font-size: 14px; color: #64748b;">Email:</span>
                <p style="font-weight: 500; margin: 0;">{{ user.email }}</p>
            </div>
            <div>
                <span style="font-size: 14px; color: #64748b;">Member Since:</span>
                <p style="font-weight: 500; margin: 0;">{{ user.created_at.strftime('%B %Y') }}</p>
            </div>
            <div>
                <span style="font-size: 14px; color: #64748b;">Last Login:</span>
                <p style="font-weight: 500; margin: 0;">
                    {% if user.last_login %}
                        {{ user.last_login.strftime('%B %d, %Y at %I:%M %p') }}
                    {% else %}
                        Never
                    {% endif %}
                </p>
            </div>
            <div>
                <span style="font-size: 14px; color: #64748b;">Total Logins:</span>
                <p style="font-weight: 500; margin: 0;">{{ user.login_count }}</p>
            </div>
        </div>
    </div>

    <!-- Profile Form -->
    <form action="{{ url_for('auth.update_profile') }}" method="post">
        <h3 style="font-size: 16px; font-weight: 600; color: #334155; margin-bottom: 16px;">Personal Information</h3>
        
        <div class="grid-2 mb-16">
            <div>
                <label class="form-label">Full Name</label>
                <input type="text" name="full_name" value="{{ user.full_name or '' }}" class="form-input">
            </div>
            <div>
                <label class="form-label">Phone Number</label>
                <input type="tel" name="phone" value="{{ user.phone or '' }}" class="form-input">
            </div>
        </div>

        <div class="mb-16">
            <label class="form-label">Location</label>
            <input type="text" name="location" value="{{ user.location or '' }}" class="form-input" 
                   placeholder="City, Country">
        </div>

        <h3 style="font-size: 16px; font-weight: 600; color: #334155; margin-bottom: 16px;">Career Information</h3>
        
        <div class="grid-2 mb-16">
            <div>
                <label class="form-label">Current Position</label>
                <input type="text" name="current_position" value="{{ profile.current_position or '' }}" class="form-input">
            </div>
            <div>
                <label class="form-label">Current Company</label>
                <input type="text" name="current_company" value="{{ profile.current_company or '' }}" class="form-input">
            </div>
        </div>

        <div class="grid-2 mb-16">
            <div>
                <label class="form-label">Years of Experience</label>
                <input type="number" name="years_of_experience" value="{{ profile.years_of_experience or '' }}" class="form-input" min="0" max="50">
            </div>
            <div>
                <label class="form-label">Education Level</label>
                <select name="education_level" class="form-input">
                    <option value="">Select education level</option>
                    <option value="high_school" {% if profile.education_level == 'high_school' %}selected{% endif %}>High School</option>
                    <option value="associate" {% if profile.education_level == 'associate' %}selected{% endif %}>Associate Degree</option>
                    <option value="bachelor" {% if profile.education_level == 'bachelor' %}selected{% endif %}>Bachelor's Degree</option>
                    <option value="master" {% if profile.education_level == 'master' %}selected{% endif %}>Master's Degree</option>
                    <option value="phd" {% if profile.education_level == 'phd' %}selected{% endif %}>PhD</option>
                </select>
            </div>
        </div>

        <div class="mb-16">
            <label class="form-label">Skills</label>
            <textarea name="skills" rows="2" class="form-input" 
                      placeholder="Python, JavaScript, React, etc.">{{ profile.skills or '' }}</textarea>
        </div>

        <div class="mb-16">
            <label class="form-label">Experience Summary</label>
            <textarea name="experience_summary" rows="3" class="form-input" 
                      placeholder="Brief summary of your work experience">{{ profile.experience_summary or '' }}</textarea>
        </div>

        <h3 style="font-size: 16px; font-weight: 600; color: #334155; margin-bottom: 16px;">Job Search Preferences</h3>
        
        <div class="grid-2 mb-16">
            <div>
                <label class="form-label">Preferred Job Titles</label>
                <input type="text" name="preferred_job_titles" value="{{ profile.preferred_job_titles or '' }}" class="form-input" 
                       placeholder="Software Developer, Data Analyst">
            </div>
            <div>
                <label class="form-label">Preferred Locations</label>
                <input type="text" name="preferred_locations" value="{{ profile.preferred_locations or '' }}" class="form-input" 
                       placeholder="Remote, New York, London">
            </div>
        </div>

        <div class="grid-2 mb-16">
            <div>
                <label class="form-label">Job Type Preference</label>
                <select name="job_type_preference" class="form-input">
                    <option value="">Any</option>
                    <option value="full-time" {% if profile.job_type_preference == 'full-time' %}selected{% endif %}>Full-time</option>
                    <option value="part-time" {% if profile.job_type_preference == 'part-time' %}selected{% endif %}>Part-time</option>
                    <option value="contract" {% if profile.job_type_preference == 'contract' %}selected{% endif %}>Contract</option>
                    <option value="freelance" {% if profile.job_type_preference == 'freelance' %}selected{% endif %}>Freelance</option>
                </select>
            </div>
            <div>
                <label class="form-label">Remote Preference</label>
                <select name="remote_preference" class="form-input">
                    <option value="">Any</option>
                    <option value="remote" {% if profile.remote_preference == 'remote' %}selected{% endif %}>Remote Only</option>
                    <option value="hybrid" {% if profile.remote_preference == 'hybrid' %}selected{% endif %}>Hybrid</option>
                    <option value="onsite" {% if profile.remote_preference == 'onsite' %}selected{% endif %}>On-site</option>
                </select>
            </div>
        </div>

        <div class="grid-2 mb-16">
            <div>
                <label class="form-label">Minimum Salary Expectation</label>
                <input type="number" name="salary_expectation_min" value="{{ profile.salary_expectation_min or '' }}" class="form-input" 
                       placeholder="50000" min="0">
            </div>
            <div>
                <label class="form-label">Maximum Salary Expectation</label>
                <input type="number" name="salary_expectation_max" value="{{ profile.salary_expectation_max or '' }}" class="form-input" 
                       placeholder="100000" min="0">
            </div>
        </div>

        <h3 style="font-size: 16px; font-weight: 600; color: #334155; margin-bottom: 16px;">Social Links</h3>
        
        <div class="mb-16">
            <label class="form-label">LinkedIn URL</label>
            <input type="url" name="linkedin_url" value="{{ profile.linkedin_url or '' }}" class="form-input" 
                   placeholder="https://linkedin.com/in/yourprofile">
        </div>

        <div class="grid-2 mb-20">
            <div>
                <label class="form-label">GitHub URL</label>
                <input type="url" name="github_url" value="{{ profile.github_url or '' }}" class="form-input" 
                       placeholder="https://github.com/yourusername">
            </div>
            <div>
                <label class="form-label">Portfolio URL</label>
                <input type="url" name="portfolio_url" value="{{ profile.portfolio_url or '' }}" class="form-input" 
                       placeholder="https://yourportfolio.com">
            </div>
        </div>

        <div class="text-right">
            <button type="submit" class="btn btn-primary" style="padding: 10px 20px;">
                <i class="fas fa-save"></i>
                Update Profile
            </button>
        </div>
    </form>

    <!-- Account Actions -->
    <div style="margin-top: 40px; padding-top: 24px; border-top: 1px solid #e2e8f0;">
        <h3 style="font-size: 16px; font-weight: 600; color: #334155; margin-bottom: 16px;">Account Actions</h3>
        
        <div style="display: flex; gap: 12px; flex-wrap: wrap;">
            <button onclick="showChangePasswordForm()" class="btn btn-secondary">
                <i class="fas fa-key"></i>
                Change Password
            </button>
            
            <a href="{{ url_for('auth.logout') }}" class="btn btn-secondary">
                <i class="fas fa-sign-out-alt"></i>
                Sign Out
            </a>
            
            <button onclick="showDeleteAccountForm()" class="btn" style="background: #fee2e2; color: #991b1b; border: 1px solid #fca5a5;">
                <i class="fas fa-trash"></i>
                Delete Account
            </button>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div id="changePasswordModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 24px; border-radius: 12px; width: 90%; max-width: 400px;">
        <h3 style="margin-bottom: 16px;">Change Password</h3>
        <form action="{{ url_for('auth.change_password') }}" method="post">
            <div class="mb-16">
                <label class="form-label">Current Password</label>
                <input type="password" name="current_password" class="form-input" required>
            </div>
            <div class="mb-16">
                <label class="form-label">New Password</label>
                <input type="password" name="new_password" class="form-input" required>
            </div>
            <div class="mb-16">
                <label class="form-label">Confirm New Password</label>
                <input type="password" name="confirm_password" class="form-input" required>
            </div>
            <div style="display: flex; gap: 12px; justify-content: flex-end;">
                <button type="button" onclick="hideChangePasswordForm()" class="btn btn-secondary">Cancel</button>
                <button type="submit" class="btn btn-primary">Change Password</button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Account Modal -->
<div id="deleteAccountModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 24px; border-radius: 12px; width: 90%; max-width: 400px;">
        <h3 style="margin-bottom: 16px; color: #991b1b;">Delete Account</h3>
        <p style="margin-bottom: 16px; color: #64748b;">This action cannot be undone. All your data will be permanently deleted.</p>
        <form action="{{ url_for('auth.delete_account') }}" method="post">
            <div class="mb-16">
                <label class="form-label">Enter your password to confirm</label>
                <input type="password" name="password" class="form-input" required>
            </div>
            <div style="display: flex; gap: 12px; justify-content: flex-end;">
                <button type="button" onclick="hideDeleteAccountForm()" class="btn btn-secondary">Cancel</button>
                <button type="submit" class="btn" style="background: #ef4444; color: white;">Delete Account</button>
            </div>
        </form>
    </div>
</div>

<script>
function showChangePasswordForm() {
    document.getElementById('changePasswordModal').style.display = 'block';
}

function hideChangePasswordForm() {
    document.getElementById('changePasswordModal').style.display = 'none';
}

function showDeleteAccountForm() {
    document.getElementById('deleteAccountModal').style.display = 'block';
}

function hideDeleteAccountForm() {
    document.getElementById('deleteAccountModal').style.display = 'none';
}

// Close modals when clicking outside
document.getElementById('changePasswordModal').addEventListener('click', function(e) {
    if (e.target === this) hideChangePasswordForm();
});

document.getElementById('deleteAccountModal').addEventListener('click', function(e) {
    if (e.target === this) hideDeleteAccountForm();
});
</script>
{% endblock %}
