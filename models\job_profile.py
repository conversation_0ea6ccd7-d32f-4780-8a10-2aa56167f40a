from .database import db
from datetime import datetime
import json

class JobProfile(db.Model):
    """Comprehensive job profile with all possible resume/application fields"""
    __tablename__ = 'job_profiles'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>Key('users.id'), nullable=False, unique=True)
    
    # Personal Information
    first_name = db.Column(db.String(100), nullable=True)
    middle_name = db.Column(db.String(100), nullable=True)
    last_name = db.Column(db.String(100), nullable=True)
    full_name = db.Column(db.String(255), nullable=True)
    date_of_birth = db.Column(db.Date, nullable=True)
    nationality = db.Column(db.String(100), nullable=True)
    gender = db.Column(db.String(20), nullable=True)
    marital_status = db.Column(db.String(50), nullable=True)
    
    # Contact Information
    email = db.Column(db.String(255), nullable=True)
    phone_primary = db.Column(db.String(50), nullable=True)
    phone_secondary = db.Column(db.String(50), nullable=True)
    address_line1 = db.Column(db.String(255), nullable=True)
    address_line2 = db.Column(db.String(255), nullable=True)
    city = db.Column(db.String(100), nullable=True)
    state_province = db.Column(db.String(100), nullable=True)
    postal_code = db.Column(db.String(20), nullable=True)
    country = db.Column(db.String(100), nullable=True)
    
    # Professional Summary
    professional_summary = db.Column(db.Text, nullable=True)
    career_objective = db.Column(db.Text, nullable=True)
    years_of_experience = db.Column(db.Integer, nullable=True)
    current_position = db.Column(db.String(255), nullable=True)
    current_company = db.Column(db.String(255), nullable=True)
    current_salary = db.Column(db.String(100), nullable=True)
    expected_salary_min = db.Column(db.Integer, nullable=True)
    expected_salary_max = db.Column(db.Integer, nullable=True)
    salary_currency = db.Column(db.String(10), default='USD')
    
    # Skills & Competencies
    technical_skills = db.Column(db.Text, nullable=True)  # JSON array
    soft_skills = db.Column(db.Text, nullable=True)  # JSON array
    programming_languages = db.Column(db.Text, nullable=True)  # JSON array
    frameworks_libraries = db.Column(db.Text, nullable=True)  # JSON array
    databases = db.Column(db.Text, nullable=True)  # JSON array
    tools_software = db.Column(db.Text, nullable=True)  # JSON array
    operating_systems = db.Column(db.Text, nullable=True)  # JSON array
    cloud_platforms = db.Column(db.Text, nullable=True)  # JSON array
    methodologies = db.Column(db.Text, nullable=True)  # JSON array
    languages_spoken = db.Column(db.Text, nullable=True)  # JSON array with proficiency
    
    # Work Experience (JSON array of experience objects)
    work_experience = db.Column(db.Text, nullable=True)
    
    # Education (JSON array of education objects)
    education = db.Column(db.Text, nullable=True)
    
    # Certifications (JSON array)
    certifications = db.Column(db.Text, nullable=True)
    
    # Projects (JSON array)
    projects = db.Column(db.Text, nullable=True)
    
    # Publications & Research
    publications = db.Column(db.Text, nullable=True)  # JSON array
    research_experience = db.Column(db.Text, nullable=True)
    
    # Awards & Achievements
    awards = db.Column(db.Text, nullable=True)  # JSON array
    achievements = db.Column(db.Text, nullable=True)  # JSON array
    
    # Professional Memberships
    professional_memberships = db.Column(db.Text, nullable=True)  # JSON array
    
    # Volunteer Experience
    volunteer_experience = db.Column(db.Text, nullable=True)  # JSON array
    
    # References
    references = db.Column(db.Text, nullable=True)  # JSON array
    
    # Job Preferences
    preferred_job_titles = db.Column(db.Text, nullable=True)  # JSON array
    preferred_industries = db.Column(db.Text, nullable=True)  # JSON array
    preferred_company_sizes = db.Column(db.Text, nullable=True)  # JSON array
    preferred_locations = db.Column(db.Text, nullable=True)  # JSON array
    remote_work_preference = db.Column(db.String(50), nullable=True)  # remote, hybrid, onsite, flexible
    job_type_preference = db.Column(db.Text, nullable=True)  # JSON array: full-time, part-time, contract, freelance
    availability_start_date = db.Column(db.Date, nullable=True)
    willing_to_relocate = db.Column(db.Boolean, default=False)
    willing_to_travel = db.Column(db.String(50), nullable=True)  # none, occasional, frequent, extensive
    visa_status = db.Column(db.String(100), nullable=True)
    work_authorization = db.Column(db.String(255), nullable=True)
    
    # Social & Online Presence
    linkedin_url = db.Column(db.String(500), nullable=True)
    github_url = db.Column(db.String(500), nullable=True)
    portfolio_url = db.Column(db.String(500), nullable=True)
    personal_website = db.Column(db.String(500), nullable=True)
    twitter_url = db.Column(db.String(500), nullable=True)
    stackoverflow_url = db.Column(db.String(500), nullable=True)
    behance_url = db.Column(db.String(500), nullable=True)
    dribbble_url = db.Column(db.String(500), nullable=True)
    
    # Additional Information
    hobbies_interests = db.Column(db.Text, nullable=True)
    additional_information = db.Column(db.Text, nullable=True)
    cover_letter_template = db.Column(db.Text, nullable=True)
    
    # Profile Status
    profile_completion_score = db.Column(db.Float, default=0.0)
    last_updated_from_resume = db.Column(db.DateTime, nullable=True)
    auto_update_enabled = db.Column(db.Boolean, default=True)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<JobProfile {self.full_name}>'
    
    def to_dict(self):
        """Convert profile to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'full_name': self.full_name,
            'email': self.email,
            'phone_primary': self.phone_primary,
            'current_position': self.current_position,
            'years_of_experience': self.years_of_experience,
            'technical_skills': json.loads(self.technical_skills) if self.technical_skills else [],
            'preferred_job_titles': json.loads(self.preferred_job_titles) if self.preferred_job_titles else [],
            'profile_completion_score': self.profile_completion_score,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def calculate_completion_score(self):
        """Calculate profile completion percentage"""
        total_fields = 0
        completed_fields = 0
        
        # Essential fields (higher weight)
        essential_fields = [
            (self.full_name, 5), (self.email, 5), (self.phone_primary, 3),
            (self.professional_summary, 4), (self.technical_skills, 5),
            (self.work_experience, 5), (self.education, 4),
            (self.preferred_job_titles, 4), (self.preferred_locations, 3)
        ]
        
        # Important fields (medium weight)
        important_fields = [
            (self.current_position, 3), (self.years_of_experience, 2),
            (self.certifications, 2), (self.projects, 3),
            (self.linkedin_url, 2), (self.github_url, 2)
        ]
        
        # Optional fields (lower weight)
        optional_fields = [
            (self.address_line1, 1), (self.city, 1), (self.country, 1),
            (self.soft_skills, 1), (self.languages_spoken, 1),
            (self.portfolio_url, 1), (self.hobbies_interests, 1)
        ]
        
        all_fields = essential_fields + important_fields + optional_fields
        
        for field_value, weight in all_fields:
            total_fields += weight
            if field_value:
                if isinstance(field_value, str) and field_value.strip():
                    completed_fields += weight
                elif isinstance(field_value, (int, float)) and field_value > 0:
                    completed_fields += weight
                elif field_value:  # For other types like dates, booleans
                    completed_fields += weight
        
        score = (completed_fields / total_fields * 100) if total_fields > 0 else 0
        self.profile_completion_score = round(score, 1)
        return self.profile_completion_score
    
    def update_from_resume_data(self, parsed_data):
        """Update profile from parsed resume data"""
        try:
            personal_info = parsed_data.get('personal_info', {})
            
            # Personal Information
            if personal_info.get('name'):
                name_parts = personal_info['name'].split()
                if len(name_parts) >= 2:
                    self.first_name = name_parts[0]
                    self.last_name = name_parts[-1]
                    if len(name_parts) > 2:
                        self.middle_name = ' '.join(name_parts[1:-1])
                self.full_name = personal_info['name']
            
            # Contact Information
            if personal_info.get('email'):
                self.email = personal_info['email']
            if personal_info.get('phone'):
                self.phone_primary = personal_info['phone']
            if personal_info.get('location'):
                location_parts = personal_info['location'].split(',')
                if len(location_parts) >= 2:
                    self.city = location_parts[0].strip()
                    self.state_province = location_parts[1].strip()
                if len(location_parts) >= 3:
                    self.country = location_parts[2].strip()
            
            # Professional Summary
            if parsed_data.get('summary'):
                self.professional_summary = parsed_data['summary']
            
            # Experience
            if parsed_data.get('years_of_experience'):
                try:
                    years = int(re.search(r'\d+', str(parsed_data['years_of_experience'])).group())
                    self.years_of_experience = years
                except:
                    pass
            
            # Work Experience
            if parsed_data.get('experience'):
                if isinstance(parsed_data['experience'], list):
                    self.work_experience = json.dumps(parsed_data['experience'])
                else:
                    # Convert string experience to structured format
                    experience_obj = {
                        'summary': str(parsed_data['experience']),
                        'positions': []
                    }
                    self.work_experience = json.dumps([experience_obj])
            
            # Skills
            if parsed_data.get('skills'):
                skills = parsed_data['skills']
                if isinstance(skills, dict):
                    if skills.get('technical'):
                        self.technical_skills = json.dumps(skills['technical'])
                    if skills.get('programming'):
                        self.programming_languages = json.dumps(skills['programming'])
                    if skills.get('soft'):
                        self.soft_skills = json.dumps(skills['soft'])
                elif isinstance(skills, list):
                    # Categorize skills automatically
                    tech_skills = []
                    prog_languages = []
                    for skill in skills:
                        skill_lower = skill.lower()
                        if any(lang in skill_lower for lang in ['python', 'javascript', 'java', 'c++', 'c#', 'php', 'ruby', 'go']):
                            prog_languages.append(skill)
                        else:
                            tech_skills.append(skill)
                    
                    if tech_skills:
                        self.technical_skills = json.dumps(tech_skills)
                    if prog_languages:
                        self.programming_languages = json.dumps(prog_languages)
            
            # Education
            if parsed_data.get('education'):
                self.education = json.dumps(parsed_data['education'])
            
            # Projects
            if parsed_data.get('projects'):
                self.projects = json.dumps(parsed_data['projects'])
            
            # Certifications
            if parsed_data.get('certifications'):
                self.certifications = json.dumps(parsed_data['certifications'])
            
            # Job Titles
            if parsed_data.get('job_titles'):
                self.preferred_job_titles = json.dumps(parsed_data['job_titles'])
            
            # Social Links
            if personal_info.get('linkedin'):
                self.linkedin_url = personal_info['linkedin']
            if personal_info.get('github'):
                self.github_url = personal_info['github']
            
            # Update timestamps
            self.last_updated_from_resume = datetime.utcnow()
            self.updated_at = datetime.utcnow()
            
            # Calculate completion score
            self.calculate_completion_score()
            
            return True
            
        except Exception as e:
            print(f"Error updating profile from resume data: {e}")
            return False
    
    @classmethod
    def get_or_create_for_user(cls, user_id):
        """Get existing profile or create new one for user"""
        profile = cls.query.filter_by(user_id=user_id).first()
        if not profile:
            profile = cls(user_id=user_id)
            db.session.add(profile)
            db.session.commit()
        return profile
