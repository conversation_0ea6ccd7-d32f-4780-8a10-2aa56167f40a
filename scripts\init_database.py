"""
Database initialization script
Run this to set up the database with sample data
"""

import sys
import os

# Add parent directory to path to import models
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask
from models import init_db, User, JobWebsite, db
from utils.database_manager import DatabaseManager

def initialize_database():
    """Initialize database with sample data"""
    
    print("🚀 Initializing Job Application Database")
    print("=" * 50)
    
    # Create Flask app for database context
    app = Flask(__name__)
    
    # Initialize database
    database = init_db(app)
    
    with app.app_context():
        print("\n📊 Database Statistics:")
        from models.database import get_db_stats
        stats = get_db_stats()
        for table, count in stats.items():
            print(f"  {table.title()}: {count}")
        
        print("\n👤 Default User:")
        user = User.get_or_create_default()
        print(f"  Email: {user.email}")
        print(f"  Name: {user.full_name}")
        print(f"  ID: {user.id}")
        
        print("\n🌐 Job Websites:")
        websites = JobWebsite.query.filter_by(user_id=user.id).all()
        if not websites:
            websites = JobWebsite.create_defaults_for_user(user.id)
            print("  Created default websites:")
        else:
            print("  Existing websites:")
        
        for website in websites:
            status = "✅ Active" if website.active else "❌ Inactive"
            print(f"  - {website.name} ({website.url}) {status}")
        
        print("\n🎯 Sample Data:")
        applications = DatabaseManager.get_applied_jobs()
        if not applications:
            print("  Creating sample job applications...")
            DatabaseManager.simulate_job_applications()
            applications = DatabaseManager.get_applied_jobs()
        
        print(f"  Total Applications: {len(applications)}")
        for app in applications[:3]:  # Show first 3
            print(f"  - {app['title']} at {app['company']} ({app['status']})")
        
        print("\n📈 Dashboard Stats:")
        stats = DatabaseManager.get_dashboard_stats()
        for key, value in stats.items():
            print(f"  {key.replace('_', ' ').title()}: {value}")
        
        print("\n✅ Database initialization complete!")
        print(f"📁 Database file: instance/jobapp.db")
        print("🔍 You can view the database using DB Browser for SQLite")

if __name__ == "__main__":
    initialize_database()
