"""
Supabase-compatible JobProfile model
"""
from typing import Optional, Dict, Any
from datetime import datetime, date
import uuid
import logging
from .supabase_database import SupabaseModel

logger = logging.getLogger(__name__)

class JobProfile(SupabaseModel):
    """Comprehensive job profile with all possible resume/application fields"""
    
    table_name = "job_profiles"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Set default values
        self.id = kwargs.get('id', str(uuid.uuid4()))
        self.user_id = kwargs.get('user_id', '')
        
        # Personal Information
        self.first_name = kwargs.get('first_name', '')
        self.middle_name = kwargs.get('middle_name', '')
        self.last_name = kwargs.get('last_name', '')
        self.full_name = kwargs.get('full_name', '')
        self.date_of_birth = kwargs.get('date_of_birth')
        self.nationality = kwargs.get('nationality', '')
        self.gender = kwargs.get('gender', '')
        self.marital_status = kwargs.get('marital_status', '')
        
        # Contact Information
        self.email = kwargs.get('email', '')
        self.phone_primary = kwargs.get('phone_primary', '')
        self.phone_secondary = kwargs.get('phone_secondary', '')
        self.address_line1 = kwargs.get('address_line1', '')
        self.address_line2 = kwargs.get('address_line2', '')
        self.city = kwargs.get('city', '')
        self.state_province = kwargs.get('state_province', '')
        self.postal_code = kwargs.get('postal_code', '')
        self.country = kwargs.get('country', '')
        
        # Professional Information
        self.professional_summary = kwargs.get('professional_summary', '')
        self.career_objective = kwargs.get('career_objective', '')
        self.years_of_experience = kwargs.get('years_of_experience')
        self.current_position = kwargs.get('current_position', '')
        self.current_company = kwargs.get('current_company', '')
        self.current_salary = kwargs.get('current_salary', '')
        self.expected_salary_min = kwargs.get('expected_salary_min')
        self.expected_salary_max = kwargs.get('expected_salary_max')
        self.salary_currency = kwargs.get('salary_currency', 'USD')
        
        # Skills and Technologies
        self.technical_skills = kwargs.get('technical_skills', '')
        self.soft_skills = kwargs.get('soft_skills', '')
        self.programming_languages = kwargs.get('programming_languages', '')
        self.frameworks_libraries = kwargs.get('frameworks_libraries', '')
        self.databases = kwargs.get('databases', '')
        self.tools_software = kwargs.get('tools_software', '')
        self.operating_systems = kwargs.get('operating_systems', '')
        self.cloud_platforms = kwargs.get('cloud_platforms', '')
        self.methodologies = kwargs.get('methodologies', '')
        self.languages_spoken = kwargs.get('languages_spoken', '')
        
        # Experience and Education
        self.work_experience = kwargs.get('work_experience', '')
        self.education = kwargs.get('education', '')
        self.certifications = kwargs.get('certifications', '')
        self.projects = kwargs.get('projects', '')
        self.publications = kwargs.get('publications', '')
        self.research_experience = kwargs.get('research_experience', '')
        self.awards = kwargs.get('awards', '')
        self.achievements = kwargs.get('achievements', '')
        self.professional_memberships = kwargs.get('professional_memberships', '')
        self.volunteer_experience = kwargs.get('volunteer_experience', '')
        self.references = kwargs.get('references', '')
        
        # Job Preferences
        self.preferred_job_titles = kwargs.get('preferred_job_titles', '')
        self.preferred_industries = kwargs.get('preferred_industries', '')
        self.preferred_company_sizes = kwargs.get('preferred_company_sizes', '')
        self.preferred_locations = kwargs.get('preferred_locations', '')
        self.remote_work_preference = kwargs.get('remote_work_preference', '')
        self.job_type_preference = kwargs.get('job_type_preference', '')
        self.availability_start_date = kwargs.get('availability_start_date')
        self.willing_to_relocate = kwargs.get('willing_to_relocate', False)
        self.willing_to_travel = kwargs.get('willing_to_travel', '')
        self.visa_status = kwargs.get('visa_status', '')
        self.work_authorization = kwargs.get('work_authorization', '')
        
        # Social Links
        self.linkedin_url = kwargs.get('linkedin_url', '')
        self.github_url = kwargs.get('github_url', '')
        self.portfolio_url = kwargs.get('portfolio_url', '')
        self.personal_website = kwargs.get('personal_website', '')
        self.twitter_url = kwargs.get('twitter_url', '')
        self.stackoverflow_url = kwargs.get('stackoverflow_url', '')
        self.behance_url = kwargs.get('behance_url', '')
        self.dribbble_url = kwargs.get('dribbble_url', '')
        
        # Additional Information
        self.hobbies_interests = kwargs.get('hobbies_interests', '')
        self.additional_information = kwargs.get('additional_information', '')
        self.cover_letter_template = kwargs.get('cover_letter_template', '')
        
        # Profile Status
        self.profile_completion_score = kwargs.get('profile_completion_score', 0.0)
        self.last_updated_from_resume = kwargs.get('last_updated_from_resume')
        self.auto_update_enabled = kwargs.get('auto_update_enabled', True)
        
        # Timestamps
        self.created_at = kwargs.get('created_at', datetime.utcnow().isoformat())
        self.updated_at = kwargs.get('updated_at', datetime.utcnow().isoformat())
    
    @classmethod
    def get_or_create_for_user(cls, user_id: str) -> Optional['JobProfile']:
        """Get or create job profile for user"""
        try:
            profiles = cls.filter_by(user_id=user_id)
            if profiles:
                return profiles[0]
            
            # Create new profile
            profile_data = {
                'id': str(uuid.uuid4()),
                'user_id': user_id,
                'auto_update_enabled': True,
                'profile_completion_score': 0.0
            }
            return cls.create(profile_data)
            
        except Exception as e:
            logger.error(f"Error getting/creating job profile: {e}")
            return None
    
    def update_from_resume_data(self, resume_data: Dict[str, Any]) -> bool:
        """Update profile from parsed resume data"""
        try:
            if not self.auto_update_enabled:
                return False
            
            update_data = {}
            
            # Map resume data to profile fields
            field_mapping = {
                'name': 'full_name',
                'email': 'email',
                'phone': 'phone_primary',
                'address': 'address_line1',
                'city': 'city',
                'state': 'state_province',
                'country': 'country',
                'summary': 'professional_summary',
                'objective': 'career_objective',
                'skills': 'technical_skills',
                'experience': 'work_experience',
                'education': 'education',
                'certifications': 'certifications',
                'projects': 'projects',
                'linkedin': 'linkedin_url',
                'github': 'github_url',
                'portfolio': 'portfolio_url'
            }
            
            for resume_key, profile_key in field_mapping.items():
                if resume_key in resume_data and resume_data[resume_key]:
                    update_data[profile_key] = str(resume_data[resume_key])
            
            # Calculate completion score
            completion_score = self.calculate_completion_score(update_data)
            update_data['profile_completion_score'] = completion_score
            update_data['last_updated_from_resume'] = datetime.utcnow().isoformat()
            
            return self.update(update_data)
            
        except Exception as e:
            logger.error(f"Error updating profile from resume data: {e}")
            return False
    
    def calculate_completion_score(self, data: Dict[str, Any] = None) -> float:
        """Calculate profile completion score"""
        try:
            if data is None:
                data = self.to_dict()
            
            # Define important fields and their weights
            important_fields = {
                'full_name': 5,
                'email': 5,
                'phone_primary': 3,
                'professional_summary': 10,
                'technical_skills': 10,
                'work_experience': 15,
                'education': 10,
                'preferred_job_titles': 8,
                'preferred_locations': 5,
                'linkedin_url': 3,
                'github_url': 3
            }
            
            total_weight = sum(important_fields.values())
            earned_weight = 0
            
            for field, weight in important_fields.items():
                if field in data and data[field] and str(data[field]).strip():
                    earned_weight += weight
            
            return round((earned_weight / total_weight) * 100, 1)
            
        except Exception as e:
            logger.error(f"Error calculating completion score: {e}")
            return 0.0
    
    def get_user(self):
        """Get the user for this profile"""
        from .supabase_auth import User
        return User.get_by_id(self.user_id)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert profile to dictionary"""
        result = super().to_dict()
        
        # Handle date fields
        if self.date_of_birth and isinstance(self.date_of_birth, str):
            result['date_of_birth'] = self.date_of_birth
        if self.availability_start_date and isinstance(self.availability_start_date, str):
            result['availability_start_date'] = self.availability_start_date
        
        return result
