"""
Test script to check if all imports work correctly
"""

import sys
import os

# Add the parent directory to Python path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def test_imports():
    """Test all imports"""
    
    print("🧪 Testing Imports")
    print("=" * 20)
    print(f"Current directory: {os.getcwd()}")
    print(f"Parent directory: {parent_dir}")
    print(f"Python path: {sys.path[:3]}...")
    
    try:
        print("\n1. Testing Flask import...")
        from flask import Flask
        print("   ✅ Flask imported successfully")
        
        print("\n2. Testing database import...")
        from models.database import db, init_db
        print("   ✅ Database imports successful")
        
        print("\n3. Testing auth models...")
        from models.auth import User, UserProfile
        print("   ✅ Auth models imported successfully")
        
        print("\n4. Testing other models...")
        from models.website import JobWebsite
        from models.job import Job
        from models.application import Application
        from models.resume import Resume
        print("   ✅ All models imported successfully")
        
        print("\n5. Testing Flask-Login...")
        from flask_login import LoginManager
        print("   ✅ Flask-Login imported successfully")
        
        print("\n✅ All imports working correctly!")
        print("You can now run: python scripts/simple_setup.py")
        
    except ImportError as e:
        print(f"\n❌ Import error: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure you're in the project root directory")
        print("2. Check if all required files exist:")
        
        required_files = [
            'models/__init__.py',
            'models/database.py',
            'models/auth.py',
            'models/website.py',
            'models/job.py',
            'models/application.py',
            'models/resume.py'
        ]
        
        for file_path in required_files:
            full_path = os.path.join(parent_dir, file_path)
            exists = os.path.exists(full_path)
            status = "✅" if exists else "❌"
            print(f"   {status} {file_path}")
        
        print("\n3. Install required packages:")
        print("   pip install Flask Flask-SQLAlchemy Flask-Login")

if __name__ == "__main__":
    test_imports()
