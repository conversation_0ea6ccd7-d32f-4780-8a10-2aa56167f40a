from .database import db
from datetime import datetime
from sqlalchemy.dialects.sqlite import JSON

class Application(db.Model):
    __tablename__ = 'applications'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('users.id'), nullable=False)
    job_id = db.Column(db.Integer, db.<PERSON>ey('jobs.id'), nullable=False)
    
    # Application status
    status = db.Column(db.String(50), default='applied')  # applied, pending, rejected, interview, offer
    
    # Application details
    cover_letter = db.Column(db.Text, nullable=True)
    notes = db.Column(db.Text, nullable=True)
    
    # Automation data
    automation_log = db.Column(JSON, nullable=True)  # Log of automation steps
    form_data = db.Column(JSON, nullable=True)  # Data filled in application form
    
    # Timestamps
    applied_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<Application {self.job.title} - {self.status}>'
    
    def to_dict(self):
        """Convert application to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'job_id': self.job_id,
            'job_title': self.job.title if self.job else None,
            'company': self.job.company if self.job else None,
            'job_url': self.job.job_url if self.job else None,
            'website_name': self.job.website.name if self.job and self.job.website else None,
            'status': self.status,
            'cover_letter': self.cover_letter,
            'notes': self.notes,
            'automation_log': self.automation_log or [],
            'form_data': self.form_data or {},
            'applied_at': self.applied_at.isoformat() if self.applied_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def update_status(self, new_status, notes=None):
        """Update application status"""
        self.status = new_status
        if notes:
            self.notes = notes
        self.updated_at = datetime.utcnow()
        db.session.commit()
    
    def add_automation_log(self, step, success=True, details=None):
        """Add automation log entry"""
        if not self.automation_log:
            self.automation_log = []
        
        log_entry = {
            'step': step,
            'success': success,
            'details': details,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        self.automation_log.append(log_entry)
        self.updated_at = datetime.utcnow()
        db.session.commit()
    
    @classmethod
    def create_from_automation(cls, user_id, job_id, form_data=None, automation_log=None):
        """Create application from automation"""
        application = cls(
            user_id=user_id,
            job_id=job_id,
            status='applied',
            form_data=form_data or {},
            automation_log=automation_log or []
        )
        
        db.session.add(application)
        db.session.commit()
        return application
    
    @classmethod
    def get_stats_for_user(cls, user_id):
        """Get application statistics for user"""
        applications = cls.query.filter_by(user_id=user_id).all()
        
        stats = {
            'total': len(applications),
            'applied': len([a for a in applications if a.status == 'applied']),
            'pending': len([a for a in applications if a.status == 'pending']),
            'interview': len([a for a in applications if a.status == 'interview']),
            'rejected': len([a for a in applications if a.status == 'rejected']),
            'offer': len([a for a in applications if a.status == 'offer'])
        }
        
        return stats
