from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_login import Lo<PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user, UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
import os
import re
import asyncio
import threading
from datetime import datetime
from utils.resume_parser import EnhancedResumeParser
from utils.browser_automation import AsyncJobAutomationManager
import json
from models.supabase_job_profile import JobProfile
from utils.ai_resume_parser import AIResumeParser
import logging

# Import Supabase models and configuration
from models.supabase_config import init_supabase
from models.supabase_database import init_supabase_db, get_db_stats
from models.supabase_auth import User, UserProfile
from models.supabase_resume import Resume
from models.supabase_job import Job
from models.supabase_application import Application
from models.supabase_website import JobWebsite

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this-in-production'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Create upload directory
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Initialize Supabase
supabase_client = init_supabase(app)
init_supabase_db(app)

# Initialize login manager
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Please log in to access this page.'

# User loader for Flask-Login
@login_manager.user_loader
def load_user(user_id):
    try:
        # Handle both UUID strings and integer IDs for backward compatibility
        if user_id and len(str(user_id)) > 10:  # Likely a UUID
            return User.get_by_id(str(user_id))
        else:
            # Legacy integer ID - return None to force re-login
            return None
    except Exception as e:
        logger.error(f"Error loading user {user_id}: {e}")
        return None

# Helper functions
def is_valid_email(email):
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def is_strong_password(password):
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"
    if not re.search(r'[A-Z]', password):
        return False, "Password must contain at least one uppercase letter"
    if not re.search(r'[a-z]', password):
        return False, "Password must contain at least one lowercase letter"
    if not re.search(r'\d', password):
        return False, "Password must contain at least one number"
    return True, "Password is strong"

def allowed_file(filename):
    ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'txt'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def create_default_websites(user_id):
    """Create default websites for user"""
    return JobWebsite.create_defaults_for_user(user_id)

def run_automation_async(user_id):
    """Run automation in background thread using real browser automation"""
    def automation_worker():
        try:
            user = User.get_by_id(user_id)
            if not user:
                return
            
            # Update status
            user.update({'automation_status': 'running'})
            
            # Get user profile data
            user_profile = {
                'full_name': user.full_name or 'User',
                'email': user.email,
                'phone': user.phone or '',
                'location': user.location or 'Remote'
            }
            
            # Get user preferences
            preferences = {
                'skills': user.skills or 'Python, JavaScript',
                'preferred_titles': user.preferred_titles or 'Developer, Engineer',
                'preferred_locations': user.preferred_locations or 'Remote',
                'experience': user.experience or ''
            }
            
            # Get active websites
            websites = JobWebsite.get_active_by_user(user_id)
            websites_data = [
                {
                    'name': w.name,
                    'url': w.url,
                    'active': w.active
                }
                for w in websites
            ]
            
            # Run real browser automation
            automation_manager = AsyncJobAutomationManager()
            results = automation_manager.run_automation_sync(user_profile, websites_data, preferences)
            
            if results['success']:
                # Save jobs and applications to database
                for app_data in results['applications']:
                    # Find the website
                    website = next((w for w in websites if w.name == app_data.get('website', 'Unknown')), websites[0] if websites else None)

                    # Create job record first
                    job = Job.create_from_scrape(
                        user_id=user_id,
                        website_id=website.id if website else None,
                        job_data={
                            'title': app_data.get('title', 'Unknown'),
                            'company': app_data.get('company', 'Unknown'),
                            'job_url': app_data.get('url', ''),
                            'description': app_data.get('description', ''),
                            'salary_range': app_data.get('salary', ''),
                            'location': app_data.get('location', ''),
                            'job_type': app_data.get('job_type'),
                            'remote_type': app_data.get('remote_type'),
                            'external_id': app_data.get('external_id'),
                            'tags': app_data.get('tags', [])
                        }
                    )

                    # Create application record
                    if job and app_data.get('applied', False):
                        Application.create_from_automation(
                            user_id=user_id,
                            job_id=job.id,
                            form_data=app_data.get('form_data', {}),
                            automation_log=app_data.get('log', [])
                        )

                # Update website stats
                for website in websites:
                    website.update_scrape_info()

                # Update user status
                user.update({'automation_status': 'completed'})
                
                logger.info(f"✅ Automation completed for user {user_id}: {results['total_found']} jobs found, {results['total_applied']} applied")
            else:
                user.update({'automation_status': 'error'})
                logger.error(f"❌ Automation failed for user {user_id}: {results.get('error', 'Unknown error')}")
            
        except Exception as e:
            user = User.get_by_id(user_id)
            if user:
                user.update({'automation_status': 'error'})
            logger.error(f"❌ Automation error for user {user_id}: {e}")
    
    thread = threading.Thread(target=automation_worker)
    thread.daemon = True
    thread.start()

# Routes
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('upload'))
    return redirect(url_for('login'))

@app.route('/signup', methods=['GET', 'POST'])
def signup():
    if current_user.is_authenticated:
        return redirect(url_for('upload'))
    
    if request.method == 'POST':
        email = request.form.get('email', '').strip().lower()
        password = request.form.get('password', '')
        confirm_password = request.form.get('confirm_password', '')
        full_name = request.form.get('full_name', '').strip()
        
        # Validation
        if not email or not password or not full_name:
            flash('All fields are required', 'error')
            return render_template('auth/signup.html')
        
        if not is_valid_email(email):
            flash('Please enter a valid email address', 'error')
            return render_template('auth/signup.html')
        
        if password != confirm_password:
            flash('Passwords do not match', 'error')
            return render_template('auth/signup.html')
        
        is_strong, message = is_strong_password(password)
        if not is_strong:
            flash(message, 'error')
            return render_template('auth/signup.html')
        
        # Check if user already exists
        if User.get_by_email(email):
            flash('Email address already registered', 'error')
            return render_template('auth/signup.html')
        
        try:
            # Create new user
            user = User.create_user(email=email, password=password, full_name=full_name)
            
            if user:
                # Create default websites
                create_default_websites(user.id)
                
                # Log in the user
                login_user(user)
                user.record_login()
                
                flash('Account created successfully! Welcome to AI Job Application Assistant.', 'success')
                return redirect(url_for('upload'))
            else:
                flash('An error occurred while creating your account. Please try again.', 'error')
                return render_template('auth/signup.html')
            
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            flash('An error occurred while creating your account. Please try again.', 'error')
            return render_template('auth/signup.html')

    return render_template('auth/signup.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('upload'))

    if request.method == 'POST':
        email = request.form.get('email', '').strip().lower()
        password = request.form.get('password', '')
        remember = bool(request.form.get('remember'))

        if not email or not password:
            flash('Email and password are required', 'error')
            return render_template('auth/login.html')

        user = User.get_by_email(email)

        if user and user.check_password(password):
            if not user.is_active:
                flash('Your account has been deactivated. Please contact support.', 'error')
                return render_template('auth/login.html')

            login_user(user, remember=remember)
            user.record_login()

            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)

            flash(f'Welcome back, {user.full_name}!', 'success')
            return redirect(url_for('upload'))
        else:
            flash('Invalid email or password', 'error')

    return render_template('auth/login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('You have been logged out successfully', 'info')
    return redirect(url_for('login'))

@app.route('/upload')
@login_required
def upload():
    # Get user's resumes
    resumes = Resume.get_by_user(current_user.id)
    return render_template('upload.html', current_page='upload', resumes=resumes)

@app.route('/upload-resume', methods=['POST'])
@login_required
def upload_resume():
    if 'resume' not in request.files:
        flash('No file selected', 'error')
        return redirect(url_for('upload'))

    file = request.files['resume']
    if not file.filename or file.filename == '':
        flash('No file selected', 'error')
        return redirect(url_for('upload'))

    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename) if file.filename else 'unknown_file'
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        safe_filename = f"{current_user.id}_{timestamp}_{filename}"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], safe_filename)
        file.save(file_path)

        # Save resume record
        resume = Resume.create_resume(
            user_id=current_user.id,
            filename=filename,
            file_path=file_path,
            file_size=os.path.getsize(file_path),
            file_type=filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
        )

        if resume:
            flash('Resume uploaded successfully! Processing will begin shortly.', 'success')
        else:
            flash('Failed to save resume. Please try again.', 'error')
    else:
        flash('Invalid file type. Please upload PDF, DOC, DOCX, or TXT files only.', 'error')

    return redirect(url_for('upload'))

@app.route('/jobs')
@login_required
def jobs():
    # Get user's jobs
    user_jobs = Job.get_by_user(current_user.id)
    return render_template('jobs.html', current_page='jobs', jobs=user_jobs)

@app.route('/websites')
@login_required
def websites():
    # Get user's websites
    user_websites = JobWebsite.get_by_user(current_user.id)
    return render_template('websites.html', current_page='websites', websites=user_websites)

@app.route('/automate')
@login_required
def automate():
    return render_template('automate.html', current_page='automate')

@app.route('/start-automation', methods=['POST'])
@login_required
def start_automation():
    try:
        # Check if automation is already running
        if current_user.automation_status == 'running':
            flash('Automation is already running. Please wait for it to complete.', 'warning')
            return redirect(url_for('automate'))

        # Start automation in background
        run_automation_async(current_user.id)

        flash('Job automation started! You will be notified when it completes.', 'success')
        return redirect(url_for('automation_status'))

    except Exception as e:
        logger.error(f"Error starting automation: {e}")
        flash('Failed to start automation. Please try again.', 'error')
        return redirect(url_for('automate'))

@app.route('/automation-status')
@login_required
def automation_status():
    return render_template('automation_status.html', current_page='automate')

@app.route('/api/automation-status')
@login_required
def api_automation_status():
    """API endpoint to get automation status"""
    user = User.get_by_id(current_user.id)
    if user:
        return jsonify({
            'status': user.automation_status or 'idle',
            'message': f'Automation status: {user.automation_status or "idle"}'
        })
    return jsonify({'status': 'error', 'message': 'User not found'})

@app.route('/profile')
@login_required
def profile():
    """User profile page"""
    return render_template('profile.html', current_page='profile')

if __name__ == '__main__':
    app.run(debug=True)
