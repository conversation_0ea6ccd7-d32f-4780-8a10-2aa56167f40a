from .database import db
from datetime import datetime

class JobWebsite(db.Model):
    __tablename__ = 'job_websites'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False)
    
    # Website information
    name = db.Column(db.String(100), nullable=False)
    url = db.Column(db.String(500), nullable=False)
    icon = db.Column(db.String(50), default='globe')
    
    # Status
    active = db.Column(db.<PERSON>, default=True)
    last_scraped = db.Column(db.DateTime, nullable=True)
    scrape_count = db.Column(db.Integer, default=0)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    jobs = db.relationship('Job', backref='website', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<JobWebsite {self.name}>'
    
    def to_dict(self):
        """Convert website to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'name': self.name,
            'url': self.url,
            'icon': self.icon,
            'active': self.active,
            'last_scraped': self.last_scraped.isoformat() if self.last_scraped else None,
            'scrape_count': self.scrape_count,
            'job_count': len(self.jobs),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def toggle_active(self):
        """Toggle website active status"""
        self.active = not self.active
        self.updated_at = datetime.utcnow()
        db.session.commit()
        return self.active
    
    def update_scrape_info(self):
        """Update scraping information"""
        self.last_scraped = datetime.utcnow()
        self.scrape_count += 1
        self.updated_at = datetime.utcnow()
        db.session.commit()
    
    @classmethod
    def get_active_for_user(cls, user_id):
        """Get active websites for user"""
        return cls.query.filter_by(user_id=user_id, active=True).all()
    
    @classmethod
    def create_defaults_for_user(cls, user_id):
        """Create default websites for user"""
        defaults = [
            {'name': 'RemoteOK', 'url': 'https://remoteok.io', 'icon': 'globe'},
            {'name': 'We Work Remotely', 'url': 'https://weworkremotely.com', 'icon': 'briefcase'},
            {'name': 'AngelList', 'url': 'https://angel.co', 'icon': 'trending-up'}
        ]
        
        websites = []
        for default in defaults:
            website = cls(
                user_id=user_id,
                name=default['name'],
                url=default['url'],
                icon=default['icon'],
                active=True
            )
            db.session.add(website)
            websites.append(website)
        
        db.session.commit()
        return websites
