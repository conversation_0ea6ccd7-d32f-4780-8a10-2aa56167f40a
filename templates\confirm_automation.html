{% extends "base.html" %}

{% block title %}Confirm Automation - Resume Job Automation{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h4 class="mb-0"><i class="fas fa-robot me-2"></i>Ready to Start Job Automation</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Review your information before starting automation:</strong>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">Personal Information</h6>
                        <ul class="list-unstyled">
                            <li><strong>Name:</strong> {{ user_data.full_name }}</li>
                            <li><strong>Email:</strong> {{ user_data.email }}</li>
                            <li><strong>Phone:</strong> {{ user_data.phone }}</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Job Preferences</h6>
                        <ul class="list-unstyled">
                            <li><strong>Titles:</strong> {{ user_data.preferred_titles }}</li>
                            <li><strong>Locations:</strong> {{ user_data.locations }}</li>
                        </ul>
                    </div>
                </div>
                
                <div class="mb-3">
                    <h6 class="text-muted">Skills</h6>
                    <p>{{ user_data.skills }}</p>
                </div>
                
                <div class="mb-4">
                    <h6 class="text-muted">Experience</h6>
                    <p>{{ user_data.experience }}</p>
                </div>
                
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Important:</strong> The automation will search for jobs and fill out application forms 
                    but will NOT submit them automatically. You'll have final control over each application.
                </div>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="{{ url_for('edit_resume') }}" class="btn btn-outline-secondary me-md-2">
                        <i class="fas fa-edit me-2"></i>Edit Information
                    </a>
                    <form action="{{ url_for('start_automation') }}" method="post" class="d-inline">
                        <button type="submit" class="btn btn-warning" onclick="this.disabled=true; this.innerHTML='<i class=\'fas fa-spinner fa-spin me-2\'></i>Starting Automation...'; this.form.submit();">
                            <i class="fas fa-play me-2"></i>Start Automation
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
