"""
Enhanced Resume Parser with Better Extraction and Profile Updates
"""

import os
import re
import json
from typing import Dict, List, Optional, Tuple
import PyPDF2
import docx
from datetime import datetime
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedResumeParser:
    """Enhanced resume parser with improved extraction methods"""
    
    def __init__(self):
        self.skills_database = self._load_skills_database()
        self.job_titles = self._load_job_titles()
        
    def _load_skills_database(self) -> List[str]:
        """Load comprehensive skills database"""
        return [
            # Programming Languages
            'Python', 'JavaScript', 'Java', 'C++', 'C#', 'PHP', 'Ruby', 'Go', 'Rust', 'Swift',
            'Kotlin', 'TypeScript', 'Scala', 'R', 'MATLAB', 'Perl', 'Shell', 'Bash', 'SQL',
            
            # Web Technologies
            'HTML', 'CSS', 'React', 'Angular', 'Vue.js', 'Node.js', 'Express', 'Django',
            'Flask', 'FastAPI', 'Spring', 'Laravel', 'Rails', 'ASP.NET', 'jQuery', 'Bootstrap',
            
            # Databases
            'MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'SQLite', 'Oracle', 'SQL Server',
            'Cassandra', 'DynamoDB', 'Elasticsearch', 'Neo4j', 'Firebase',
            
            # Cloud & DevOps
            'AWS', 'Azure', 'Google Cloud', 'Docker', 'Kubernetes', 'Jenkins', 'GitLab CI',
            'Terraform', 'Ansible', 'Chef', 'Puppet', 'Nginx', 'Apache', 'CI/CD',
            
            # Data Science & AI
            'Machine Learning', 'Deep Learning', 'TensorFlow', 'PyTorch', 'Scikit-learn',
            'Pandas', 'NumPy', 'Matplotlib', 'Seaborn', 'Jupyter', 'Data Analysis',
            'Statistics', 'NLP', 'Computer Vision', 'Neural Networks', 'AI',
            
            # Tools & Frameworks
            'Git', 'GitHub', 'GitLab', 'Jira', 'Confluence', 'Slack', 'Trello', 'Asana',
            'Figma', 'Adobe Creative Suite', 'Photoshop', 'Illustrator', 'VS Code',
            
            # Methodologies
            'Agile', 'Scrum', 'Kanban', 'DevOps', 'TDD', 'BDD', 'Microservices',
            'REST API', 'GraphQL', 'SOAP', 'API Development', 'Web Services',
            
            # Soft Skills
            'Leadership', 'Communication', 'Problem Solving', 'Team Management',
            'Project Management', 'Critical Thinking', 'Creativity', 'Adaptability'
        ]
    
    def _load_job_titles(self) -> List[str]:
        """Load common job titles for matching"""
        return [
            'Software Engineer', 'Software Developer', 'Full Stack Developer',
            'Frontend Developer', 'Backend Developer', 'Web Developer',
            'Data Scientist', 'Data Analyst', 'Machine Learning Engineer',
            'DevOps Engineer', 'Cloud Engineer', 'System Administrator',
            'Product Manager', 'Project Manager', 'Scrum Master',
            'UI/UX Designer', 'Graphic Designer', 'Technical Writer',
            'QA Engineer', 'Test Engineer', 'Security Engineer',
            'Database Administrator', 'Network Engineer', 'Mobile Developer',
            'Senior Developer', 'Lead Developer', 'Principal Engineer',
            'Technical Lead', 'Engineering Manager', 'CTO', 'Architect'
        ]
    
    def parse_resume(self, file_path: str) -> Optional[Dict]:
        """Main parsing function with enhanced extraction"""
        try:
            logger.info(f"🔍 Starting resume parsing for: {file_path}")
            
            # Extract text based on file type
            text = self._extract_text(file_path)
            if not text:
                logger.error("❌ Failed to extract text from resume")
                return None
            
            logger.info(f"✅ Extracted {len(text)} characters from resume")
            
            # Parse different sections
            parsed_data = {
                'personal_info': self._extract_personal_info(text),
                'skills': self._extract_skills(text),
                'experience': self._extract_experience(text),
                'education': self._extract_education(text),
                'projects': self._extract_projects(text),
                'certifications': self._extract_certifications(text),
                'languages': self._extract_languages(text),
                'summary': self._extract_summary(text),
                'job_titles': self._extract_job_titles(text),
                'years_of_experience': self._extract_years_of_experience(text),
                'parsed_at': datetime.utcnow().isoformat(),
                'file_path': file_path,
                'raw_text_sample': text[:500]  # First 500 chars for debugging
            }
            
            logger.info(f"✅ Resume parsing completed successfully")
            logger.info(f"📊 Extracted: {len(parsed_data['skills'])} skills, {len(parsed_data['job_titles'])} job titles")
            
            return parsed_data
            
        except Exception as e:
            logger.error(f"❌ Error parsing resume: {e}")
            return None
    
    def _extract_text(self, file_path: str) -> str:
        """Enhanced text extraction from different file formats"""
        try:
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext == '.pdf':
                return self._extract_from_pdf(file_path)
            elif file_ext in ['.doc', '.docx']:
                return self._extract_from_docx(file_path)
            elif file_ext == '.txt':
                return self._extract_from_txt(file_path)
            else:
                logger.warning(f"⚠️ Unsupported file format: {file_ext}")
                return ""
                
        except Exception as e:
            logger.error(f"❌ Error extracting text: {e}")
            return ""
    
    def _extract_from_pdf(self, file_path: str) -> str:
        """Enhanced PDF text extraction"""
        try:
            text = ""
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                logger.info(f"📄 PDF has {len(pdf_reader.pages)} pages")
                
                for i, page in enumerate(pdf_reader.pages):
                    page_text = page.extract_text()
                    text += page_text + "\n"
                    logger.debug(f"📄 Extracted {len(page_text)} characters from page {i+1}")
                    
            return text
        except Exception as e:
            logger.error(f"❌ Error reading PDF: {e}")
            return ""
    
    def _extract_from_docx(self, file_path: str) -> str:
        """Enhanced DOCX text extraction"""
        try:
            doc = docx.Document(file_path)
            text = ""
            
            # Extract from paragraphs
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            
            # Extract from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        text += cell.text + " "
                    text += "\n"
            
            logger.info(f"📄 Extracted text from DOCX with {len(doc.paragraphs)} paragraphs and {len(doc.tables)} tables")
            return text
        except Exception as e:
            logger.error(f"❌ Error reading DOCX: {e}")
            return ""
    
    def _extract_from_txt(self, file_path: str) -> str:
        """Enhanced TXT text extraction"""
        try:
            encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        text = file.read()
                        logger.info(f"📄 Successfully read TXT file with {encoding} encoding")
                        return text
                except UnicodeDecodeError:
                    continue
            
            logger.error("❌ Failed to read TXT file with any encoding")
            return ""
        except Exception as e:
            logger.error(f"❌ Error reading TXT: {e}")
            return ""
    
    def _extract_personal_info(self, text: str) -> Dict:
        """Enhanced personal information extraction"""
        personal_info = {}
        
        # Extract name with multiple patterns
        name_patterns = [
            r'^([A-Z][a-z]+ [A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)',  # Full name at start
            r'Name[:\s]+([A-Z][a-z]+ [A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)',  # After "Name:"
            r'^([A-Z][A-Z\s]+)$',  # All caps name
            r'([A-Z][a-z]+\s+[A-Z][a-z]+)',  # Simple first last name
            r'^([A-Z][a-z]+(?:\s+[A-Z]\.?)?\s+[A-Z][a-z]+)',  # With middle initial
        ]
        
        lines = text.split('\n')
        for i, line in enumerate(lines[:5]):  # Check first 5 lines
            line = line.strip()
            if len(line) > 3 and len(line) < 50:  # Reasonable name length
                for pattern in name_patterns:
                    match = re.search(pattern, line)
                    if match:
                        potential_name = match.group(1).strip()
                        # Validate it's actually a name (not a title or company)
                        if not any(word in potential_name.lower() for word in ['resume', 'cv', 'curriculum', 'inc', 'llc', 'corp']):
                            personal_info['name'] = potential_name
                            logger.info(f"✅ Extracted name: {potential_name}")
                            break
                if 'name' in personal_info:
                    break
        
        # Extract email with validation
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        email_matches = re.findall(email_pattern, text)
        if email_matches:
            # Take the first valid email
            for email in email_matches:
                if '.' in email.split('@')[1]:  # Ensure domain has extension
                    personal_info['email'] = email
                    logger.info(f"✅ Extracted email: {email}")
                    break
        
        # Extract phone with multiple patterns
        phone_patterns = [
            r'\+?1?[-.\s]?$$?([0-9]{3})$$?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})',  # US format
            r'\+?([0-9]{1,3})[-.\s]?([0-9]{3,4})[-.\s]?([0-9]{3,4})[-.\s]?([0-9]{3,4})',  # International
            r'(\+?[0-9\s\-$$$$]{10,15})',  # General phone pattern
            r'(?:phone|tel|mobile|cell)[:\s]*(\+?[0-9\s\-$$$$]{10,15})',  # After phone label
        ]
        
        for pattern in phone_patterns:
            phone_matches = re.findall(pattern, text, re.IGNORECASE)
            if phone_matches:
                if isinstance(phone_matches[0], tuple):
                    phone = ''.join(phone_matches[0])
                else:
                    phone = phone_matches[0]
                # Clean up phone number
                phone = re.sub(r'[^\d\+\-$$$$\s]', '', phone).strip()
                if len(re.sub(r'[^\d]', '', phone)) >= 10:  # At least 10 digits
                    personal_info['phone'] = phone
                    logger.info(f"✅ Extracted phone: {phone}")
                    break
        
        # Extract location with enhanced patterns
        location_patterns = [
            r'([A-Z][a-z]+,\s*[A-Z]{2}(?:\s+[0-9]{5})?)',  # City, State ZIP
            r'([A-Z][a-z]+,\s*[A-Z][a-z]+)',  # City, Country
            r'(?:location|address|city)[:\s]+([A-Z][a-z]+(?:,\s*[A-Z]{2,})?)',  # After location label
            r'([A-Z][a-z]+\s*,\s*[A-Z][a-z]+\s*,\s*[A-Z]{2,})',  # City, State, Country
        ]
        
        for pattern in location_patterns:
            location_matches = re.findall(pattern, text, re.IGNORECASE)
            if location_matches:
                location = location_matches[0].strip()
                # Validate it's a real location (not a company or title)
                if not any(word in location.lower() for word in ['inc', 'llc', 'corp', 'university', 'college']):
                    personal_info['location'] = location
                    logger.info(f"✅ Extracted location: {location}")
                    break
        
        # Extract LinkedIn
        linkedin_patterns = [
            r'linkedin\.com/in/([A-Za-z0-9-]+)',
            r'(?:linkedin|linked-in)[:\s]+([A-Za-z0-9-]+)',
        ]
        
        for pattern in linkedin_patterns:
            linkedin_match = re.search(pattern, text, re.IGNORECASE)
            if linkedin_match:
                linkedin_id = linkedin_match.group(1)
                personal_info['linkedin'] = f"https://linkedin.com/in/{linkedin_id}"
                logger.info(f"✅ Extracted LinkedIn: {personal_info['linkedin']}")
                break
        
        # Extract GitHub
        github_patterns = [
            r'github\.com/([A-Za-z0-9-]+)',
            r'(?:github|git)[:\s]+([A-Za-z0-9-]+)',
        ]
        
        for pattern in github_patterns:
            github_match = re.search(pattern, text, re.IGNORECASE)
            if github_match:
                github_id = github_match.group(1)
                personal_info['github'] = f"https://github.com/{github_id}"
                logger.info(f"✅ Extracted GitHub: {personal_info['github']}")
                break
        
        return personal_info
    
    def _extract_skills(self, text: str) -> List[str]:
        """Enhanced skills extraction"""
        found_skills = set()  # Use set to avoid duplicates
        text_lower = text.lower()
        
        # Look for skills in our database (case-insensitive)
        for skill in self.skills_database:
            # Use word boundaries to avoid partial matches
            pattern = r'\b' + re.escape(skill.lower()) + r'\b'
            if re.search(pattern, text_lower):
                found_skills.add(skill)
        
        # Look for skills in dedicated sections
        skills_section_patterns = [
            r'(?:technical\s+)?skills?[:\s]+(.*?)(?:\n\n|\n[A-Z][a-z]+:|$)',
            r'technologies?[:\s]+(.*?)(?:\n\n|\n[A-Z][a-z]+:|$)',
            r'tools?\s+(?:and\s+)?technologies?[:\s]+(.*?)(?:\n\n|\n[A-Z][a-z]+:|$)',
            r'programming\s+languages?[:\s]+(.*?)(?:\n\n|\n[A-Z][a-z]+:|$)',
            r'core\s+competencies[:\s]+(.*?)(?:\n\n|\n[A-Z][a-z]+:|$)',
        ]
        
        for pattern in skills_section_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
            for match in matches:
                # Split by common delimiters
                skills_in_section = re.split(r'[,;•\n\t\|]+', match)
                for skill in skills_in_section:
                    skill = skill.strip()
                    # Clean up skill (remove bullets, numbers, etc.)
                    skill = re.sub(r'^[\d\.\-\•\*\+\s]+', '', skill)
                    skill = re.sub(r'[^\w\s\+\#\.]', '', skill)
                    skill = skill.strip()
                    
                    if skill and len(skill) > 1 and len(skill) < 30:
                        found_skills.add(skill)
        
        skills_list = list(found_skills)
        logger.info(f"✅ Extracted {len(skills_list)} skills")
        return skills_list
    
    def _extract_job_titles(self, text: str) -> List[str]:
        """Extract job titles from experience section"""
        found_titles = set()
        
        # Look for known job titles
        for title in self.job_titles:
            pattern = r'\b' + re.escape(title.lower()) + r'\b'
            if re.search(pattern, text.lower()):
                found_titles.add(title)
        
        # Look for title patterns in experience section
        title_patterns = [
            r'(?:position|title|role)[:\s]+([A-Za-z\s]+)',
            r'([A-Za-z\s]+)\s+(?:at|@)\s+[A-Z][a-z]+',
            r'([A-Za-z\s]+)\s+\|\s+[A-Z][a-z]+',
        ]
        
        for pattern in title_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                title = match.strip()
                if len(title) > 5 and len(title) < 50:
                    found_titles.add(title)
        
        titles_list = list(found_titles)
        logger.info(f"✅ Extracted {len(titles_list)} job titles")
        return titles_list
    
    def _extract_years_of_experience(self, text: str) -> str:
        """Extract years of experience"""
        experience_patterns = [
            r'(\d+)\+?\s+years?\s+(?:of\s+)?experience',
            r'(\d+)\+?\s+years?\s+in',
            r'experience[:\s]+(\d+)\+?\s+years?',
            r'(\d+)\+?\s+years?\s+(?:working|developing)',
        ]
        
        for pattern in experience_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                years = match.group(1)
                logger.info(f"✅ Extracted years of experience: {years}")
                return f"{years}+ years"
        
        return "Not specified"
    
    def _extract_experience(self, text: str) -> str:
        """Enhanced work experience extraction"""
        experience_patterns = [
            r'(?:professional\s+)?experience[:\s]+(.*?)(?:\n\n|\neducation|\nprojects|\nskills|$)',
            r'work\s+history[:\s]+(.*?)(?:\n\n|\neducation|\nprojects|\nskills|$)',
            r'employment[:\s]+(.*?)(?:\n\n|\neducation|\nprojects|\nskills|$)',
        ]
        
        for pattern in experience_patterns:
            match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if match:
                experience_text = match.group(1).strip()
                # Clean up and summarize
                lines = [line.strip() for line in experience_text.split('\n') if line.strip()]
                # Take first few meaningful lines
                summary_lines = []
                for line in lines[:10]:
                    if len(line) > 10:  # Skip short lines
                        summary_lines.append(line)
                    if len(summary_lines) >= 3:
                        break
                
                if summary_lines:
                    experience_summary = ' '.join(summary_lines)
                    logger.info(f"✅ Extracted experience summary ({len(experience_summary)} chars)")
                    return experience_summary[:500]  # Limit length
        
        # Fallback: look for job titles and create summary
        job_titles = self._extract_job_titles(text)
        if job_titles:
            experience_summary = f"Experience in roles including: {', '.join(job_titles[:3])}"
            logger.info(f"✅ Created experience summary from job titles")
            return experience_summary
        
        return "Professional experience in software development and technology"
    
    def _extract_education(self, text: str) -> List[Dict]:
        """Enhanced education extraction"""
        education = []
        
        education_patterns = [
            r'education[:\s]+(.*?)(?:\n\n|\nexperience|\nprojects|\nskills|$)',
            r'academic\s+background[:\s]+(.*?)(?:\n\n|\nexperience|\nprojects|\nskills|$)',
            r'(bachelor|master|phd|doctorate|associate).*?(?:degree|of|in)\s+([A-Za-z\s]+)',
            r'([A-Z][a-z\s]+(?:University|College|Institute))',
        ]
        
        for pattern in education_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
            for match in matches:
                if isinstance(match, tuple):
                    education.append({
                        'degree': match[0] if len(match) > 0 else '',
                        'field': match[1] if len(match) > 1 else '',
                        'institution': match[2] if len(match) > 2 else ''
                    })
                else:
                    education.append({'institution': match.strip()})
        
        logger.info(f"✅ Extracted {len(education)} education entries")
        return education[:3]  # Limit to 3 entries
    
    def _extract_projects(self, text: str) -> List[str]:
        """Enhanced project extraction"""
        projects = []
        
        project_patterns = [
            r'projects?[:\s]+(.*?)(?:\n\n|\nexperience|\neducation|\nskills|$)',
            r'portfolio[:\s]+(.*?)(?:\n\n|\nexperience|\neducation|\nskills|$)',
            r'project[:\s]+([^\n]+)',
        ]
        
        for pattern in project_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
            for match in matches:
                if match.strip() and len(match.strip()) > 10:
                    projects.append(match.strip()[:200])  # Limit length
        
        logger.info(f"✅ Extracted {len(projects)} projects")
        return projects[:5]  # Limit to 5 projects
    
    def _extract_certifications(self, text: str) -> List[str]:
        """Enhanced certification extraction"""
        certifications = []
        
        cert_patterns = [
            r'certifications?[:\s]+(.*?)(?:\n\n|\nexperience|\neducation|\nskills|$)',
            r'certificates?[:\s]+(.*?)(?:\n\n|\nexperience|\neducation|\nskills|$)',
            r'certified\s+([A-Za-z\s]+)',
            r'(AWS|Azure|Google Cloud|Microsoft|Oracle|Cisco|CompTIA)\s+([A-Za-z\s]+)',
        ]
        
        for pattern in cert_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
            for match in matches:
                if isinstance(match, tuple):
                    cert = ' '.join(match).strip()
                else:
                    cert = match.strip()
                
                if cert and len(cert) > 5 and len(cert) < 100:
                    certifications.append(cert)
        
        logger.info(f"✅ Extracted {len(certifications)} certifications")
        return certifications[:5]  # Limit to 5 certifications
    
    def _extract_languages(self, text: str) -> List[str]:
        """Enhanced language extraction"""
        languages = set()
        
        # Programming languages
        prog_languages = ['Python', 'JavaScript', 'Java', 'C++', 'C#', 'PHP', 'Ruby', 'Go', 'Rust', 'Swift']
        for lang in prog_languages:
            if re.search(r'\b' + re.escape(lang.lower()) + r'\b', text.lower()):
                languages.add(lang)
        
        # Spoken languages
        spoken_languages = ['English', 'Spanish', 'French', 'German', 'Chinese', 'Japanese', 'Korean', 'Portuguese', 'Italian']
        for lang in spoken_languages:
            if re.search(r'\b' + re.escape(lang.lower()) + r'\b', text.lower()):
                languages.add(lang)
        
        languages_list = list(languages)
        logger.info(f"✅ Extracted {len(languages_list)} languages")
        return languages_list
    
    def _extract_summary(self, text: str) -> str:
        """Enhanced professional summary extraction"""
        summary_patterns = [
            r'(?:professional\s+)?summary[:\s]+(.*?)(?:\n\n|\nexperience|\neducation|\nskills|$)',
            r'objective[:\s]+(.*?)(?:\n\n|\nexperience|\neducation|\nskills|$)',
            r'profile[:\s]+(.*?)(?:\n\n|\nexperience|\neducation|\nskills|$)',
            r'about[:\s]+(.*?)(?:\n\n|\nexperience|\neducation|\nskills|$)',
        ]
        
        for pattern in summary_patterns:
            match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if match:
                summary = match.group(1).strip()
                # Clean up
                lines = [line.strip() for line in summary.split('\n') if line.strip()]
                if lines:
                    summary_text = ' '.join(lines[:3])  # First 3 lines
                    logger.info(f"✅ Extracted summary ({len(summary_text)} chars)")
                    return summary_text[:300]  # Limit length
        
        # Fallback: create summary from first meaningful paragraph
        paragraphs = text.split('\n\n')
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if (len(paragraph) > 100 and 
                not any(keyword in paragraph.lower() for keyword in ['email', 'phone', 'address', 'linkedin']) and
                not re.match(r'^[A-Z\s]+$', paragraph)):  # Not all caps (likely a name)
                logger.info(f"✅ Created summary from paragraph")
                return paragraph[:300] + '...' if len(paragraph) > 300 else paragraph
        
        return "Experienced professional with expertise in technology and software development"

# Test function
def test_enhanced_resume_parser():
    """Test the enhanced resume parser"""
    parser = EnhancedResumeParser()
    
    # Create a more comprehensive sample resume text
    sample_text = """
    John Doe
    Senior Software Engineer
    <EMAIL> | (************* | San Francisco, CA
    linkedin.com/in/johndoe | github.com/johndoe
    
    PROFESSIONAL SUMMARY
    Experienced software engineer with 5+ years in full-stack development.
    Expertise in Python, JavaScript, and cloud technologies.
    Proven track record of leading teams and delivering scalable solutions.
    
    TECHNICAL SKILLS
    Programming Languages: Python, JavaScript, Java, C++, TypeScript
    Web Technologies: React, Node.js, Django, Flask, HTML, CSS
    Databases: PostgreSQL, MongoDB, Redis, MySQL
    Cloud & DevOps: AWS, Docker, Kubernetes, Jenkins, CI/CD
    Tools: Git, GitHub, Jira, VS Code, Figma
    
    PROFESSIONAL EXPERIENCE
    Senior Software Engineer | TechCorp | 2020-Present
    - Developed scalable web applications using Python and React
    - Led team of 5 developers on microservices architecture
    - Implemented CI/CD pipelines reducing deployment time by 50%
    
    Software Developer | StartupXYZ | 2018-2020
    - Built REST APIs using Django and PostgreSQL
    - Implemented automated testing increasing code coverage to 90%
    - Collaborated with cross-functional teams using Agile methodology
    
    EDUCATION
    Bachelor of Science in Computer Science
    University of California, Berkeley | 2018
    
    PROJECTS
    E-commerce Platform: Built full-stack application with React and Django
    Data Analytics Tool: Created dashboard using Python and D3.js
    Mobile App: Developed iOS app using Swift and Core Data
    
    CERTIFICATIONS
    AWS Certified Solutions Architect
    Google Cloud Professional Developer
    Certified Scrum Master
    """
    
    # Test parsing
    result = parser.parse_resume("test_resume.txt")
    if result:
        print("✅ Enhanced Resume Parser Test Results:")
        print(f"Personal Info: {result['personal_info']}")
        print(f"Skills ({len(result['skills'])}): {result['skills'][:10]}")
        print(f"Job Titles: {result['job_titles']}")
        print(f"Experience: {result['experience'][:100]}...")
        print(f"Years of Experience: {result['years_of_experience']}")
    else:
        print("❌ Parsing failed")

if __name__ == "__main__":
    test_enhanced_resume_parser()
