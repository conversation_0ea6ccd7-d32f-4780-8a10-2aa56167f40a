"""
Real Browser Automation using browser-use with Gemini
"""

import asyncio
import json
import os
import random
import time
from typing import List, Dict, Optional
from datetime import datetime
import logging
from pydantic import BaseModel

from langchain_google_genai import ChatGoogleGenerativeAI
from browser_use import Agent
from browser_use.browser.browser import <PERSON>rowser
from browser_use.browser.context import BrowserContext
from playwright.async_api import Page

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class JobApplication(BaseModel):
    """Structured job application result"""
    title: str
    company: str
    url: str
    location: str = "Not specified"
    salary: str = "Not specified"
    description: str = ""
    applied: bool = False
    match_score: float = 0.0
    application_status: str = "found"
    error_message: str = ""

class JobApplicationResults(BaseModel):
    """Collection of job applications"""
    applications: List[JobApplication]
    total_found: int
    total_applied: int
    errors: List[str] = []

class EnhancedBrowserJobAutomation:
    """Enhanced browser automation with anti-detection and verification handling"""
    
    def __init__(self):
        self.llm = None
        self.browser = None
        self.context = None
        self.page = None
        self.setup_llm()
        self.setup_controller()
        
    def setup_llm(self):
        """Initialize Gemini LLM with optimal settings"""
        try:
            self.llm = ChatGoogleGenerativeAI(
                model='gemini-2.0-flash-exp',
                temperature=0.1,
                max_tokens=4000,
                top_p=0.8,
                top_k=40
            )
            logger.info("✅ Gemini LLM initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Gemini LLM: {e}")
            raise
    
    def setup_controller(self):
        """Setup is handled internally by browser-use"""
        logger.info("✅ Controller setup completed")
    
    async def setup_browser(self, headless: bool = True):
        """Setup browser session with enhanced anti-detection"""
        try:
            # Create browser instance
            self.browser = Browser(
                headless=headless,
                disable_security=True,
            )
            
            # Create context with anti-detection settings
            self.context = await self.browser.new_context(
                viewport={'width': random.randint(1200, 1920), 'height': random.randint(800, 1080)},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                extra_http_headers={
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                }
            )
            
            # Create page
            self.page = await self.context.new_page()
            
            # Set additional browser properties to avoid detection
            await self.page.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                
                // Override plugins
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
                
                // Override languages
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                });
            """)
            
            logger.info("✅ Enhanced browser session initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to setup browser: {e}")
            raise
    
    async def navigate_with_verification_handling(self, url: str, max_retries: int = 3) -> bool:
        """Navigate to URL with verification handling"""
        for attempt in range(max_retries):
            try:
                logger.info(f"🌐 Navigating to {url} (attempt {attempt + 1})")
            
                # Navigate with timeout
                await self.page.goto(url, wait_until='domcontentloaded', timeout=30000)
            
                # Wait for initial load
                await asyncio.sleep(random.uniform(3, 6))
            
                # Check if navigation was successful
                current_url = self.page.url
                if url.split('//')[1].split('/')[0] in current_url:
                    logger.info(f"✅ Successfully navigated to {url}")
                    return True
            
            except Exception as e:
                logger.warning(f"⚠️ Navigation attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(random.uniform(5, 10))
                    continue
        
        logger.error(f"❌ Failed to navigate to {url} after {max_retries} attempts")
        return False
    
    async def search_jobs_on_website(self, website_url: str, search_terms: str, location: str = "remote") -> List[JobApplication]:
        """Search for jobs on a specific website with enhanced handling"""
        try:
            # Navigate with verification handling
            if not await self.navigate_with_verification_handling(website_url):
                return []
        
            # Create specialized agent for job searching
            agent = Agent(
                task=f"""
                You are now on {website_url}. Your task is to search for jobs with these criteria:
                - Search terms: {search_terms}
                - Location: {location}
            
                Steps to follow:
                1. Find the job search functionality (search box, filters, etc.)
                2. Enter the search terms: {search_terms}
                3. Set location to: {location}
                4. Submit the search
                5. Wait for results to load completely
                6. Extract job details from the first 10 results
            
                For each job found, extract:
                - Job title
                - Company name
                - Location
                - Salary (if available)
                - Job URL/link
                - Brief description
                """,
                llm=self.llm,
                browser_context=self.context,
                use_vision=True
            )
        
            # Run the search
            result = await agent.run()
        
            # Parse results
            jobs = self._parse_job_search_results(result, website_url, search_terms)
        
            logger.info(f"✅ Found {len(jobs)} jobs on {website_url}")
            return jobs
        
        except Exception as e:
            logger.error(f"❌ Error searching jobs on {website_url}: {e}")
            return []
    
    async def apply_to_job(self, job: JobApplication, user_profile: Dict) -> JobApplication:
        """Apply to a specific job with enhanced verification handling"""
        try:
            # Navigate to job URL
            if not await self.navigate_with_verification_handling(job.url):
                job.application_status = "navigation_failed"
                job.error_message = "Failed to navigate to job URL"
                return job
        
            # Create specialized agent for job application
            agent = Agent(
                task=f"""
                You are now on the job page for: {job.title} at {job.company}
            
                Your task is to apply to this job with the following information:
                - Name: {user_profile.get('full_name', '')}
                - Email: {user_profile.get('email', '')}
                - Phone: {user_profile.get('phone', '')}
                - Location: {user_profile.get('location', '')}
            
                Steps to follow:
                1. Look for "Apply", "Apply Now", "Quick Apply", or similar buttons
                2. Click the apply button
                3. Fill out the application form with the provided information
                4. Submit the application if the process is simple
                5. Confirm the application was submitted successfully
            
                Important: Only proceed if you can complete the application simply.
                """,
                llm=self.llm,
                browser_context=self.context,
                use_vision=True
            )
        
            # Run the application process
            result = await agent.run()
        
            # Determine if application was successful
            success = self._check_application_success(result)
        
            job.applied = success
            job.application_status = "applied" if success else "failed"
        
            if success:
                logger.info(f"✅ Successfully applied to {job.title} at {job.company}")
            else:
                logger.warning(f"⚠️ Failed to apply to {job.title} at {job.company}")
            
            return job
        
        except Exception as e:
            logger.error(f"❌ Error applying to job {job.title}: {e}")
            job.applied = False
            job.application_status = "error"
            job.error_message = str(e)
            return job
    
    async def run_full_automation(self, user_profile: Dict, websites: List[Dict], preferences: Dict) -> JobApplicationResults:
        """Run complete job search and application automation"""
        all_applications = []
        errors = []
    
        try:
            # Setup browser
            await self.setup_browser(headless=True)
        
            # Build search terms from preferences
            search_terms = self._build_search_terms(preferences)
            location = preferences.get('preferred_locations', 'remote').split(',')[0].strip()
        
            logger.info(f"🚀 Starting job automation with search terms: {search_terms}")
        
            # Search jobs on each website
            for website in websites:
                if not website.get('automation_enabled', True):
                    continue
                
                try:
                    website_url = website['url']
                    logger.info(f"🔍 Searching jobs on {website['name']}")
                
                    jobs = await self.search_jobs_on_website(website_url, search_terms, location)
                
                    # Calculate match scores
                    for job in jobs:
                        job.match_score = self._calculate_match_score(job, preferences)
                
                    # Filter high-match jobs for application
                    high_match_jobs = [job for job in jobs if job.match_score >= 70]
                
                    logger.info(f"📊 Found {len(jobs)} jobs, {len(high_match_jobs)} high-match jobs")
                
                    # Apply to high-match jobs (limit to prevent overwhelming)
                    for job in high_match_jobs[:2]:
                        try:
                            applied_job = await self.apply_to_job(job, user_profile)
                            all_applications.append(applied_job)
                        
                            # Add delay between applications
                            await asyncio.sleep(random.uniform(10, 20))
                        
                        except Exception as e:
                            logger.error(f"❌ Error applying to {job.title}: {e}")
                            errors.append(f"Application error for {job.title}: {str(e)}")
                
                    # Add remaining jobs as "found" only
                    for job in jobs:
                        if job not in all_applications:
                            all_applications.append(job)
                
                    # Delay between websites
                    await asyncio.sleep(random.uniform(15, 30))
                
                except Exception as e:
                    logger.error(f"❌ Error processing website {website['name']}: {e}")
                    errors.append(f"Website error for {website['name']}: {str(e)}")
                    continue
        
            # Compile results
            total_applied = len([app for app in all_applications if app.applied])
        
            results = JobApplicationResults(
                applications=all_applications,
                total_found=len(all_applications),
                total_applied=total_applied,
                errors=errors
            )
        
            logger.info(f"🎉 Automation completed! Found: {len(all_applications)}, Applied: {total_applied}")
        
            return results
        
        except Exception as e:
            logger.error(f"❌ Fatal error in automation: {e}")
            errors.append(f"Fatal automation error: {str(e)}")
        
            return JobApplicationResults(
                applications=all_applications,
                total_found=len(all_applications),
                total_applied=0,
                errors=errors
            )
    
        finally:
            # Cleanup browser
            if hasattr(self, 'browser') and self.browser:
                try:
                    await self.browser.close()
                except:
                    pass
    
    def _build_search_terms(self, preferences: Dict) -> str:
        """Build search terms from user preferences"""
        terms = []
        
        if preferences.get('preferred_titles'):
            titles = preferences['preferred_titles'].split(',')
            terms.extend([title.strip() for title in titles[:2]])
        
        if preferences.get('skills'):
            skills = preferences['skills'].split(',')
            terms.extend([skill.strip() for skill in skills[:3]])
        
        return ' '.join(terms) if terms else 'developer'
    
    def _calculate_match_score(self, job: JobApplication, preferences: Dict) -> float:
        """Calculate job match score based on preferences"""
        score = 0.0
        
        # Title matching (40% weight)
        if preferences.get('preferred_titles'):
            titles = [t.strip().lower() for t in preferences['preferred_titles'].split(',')]
            job_title = job.title.lower()
            if any(title in job_title for title in titles):
                score += 40
        
        # Skills matching (30% weight)
        if preferences.get('skills'):
            skills = [s.strip().lower() for s in preferences['skills'].split(',')]
            job_text = f"{job.title} {job.description}".lower()
            skill_matches = sum(1 for skill in skills if skill in job_text)
            if skills:
                score += (skill_matches / len(skills)) * 30
        
        # Location matching (20% weight)
        if preferences.get('preferred_locations'):
            locations = [l.strip().lower() for l in preferences['preferred_locations'].split(',')]
            job_location = job.location.lower()
            if any(location in job_location for location in locations) or 'remote' in job_location:
                score += 20
        
        # Default bonus (10% weight)
        score += 10
        
        return min(score, 100.0)
    
    def _parse_job_search_results(self, result, website_url: str, search_terms: str) -> List[JobApplication]:
        """Enhanced parsing of job search results"""
        jobs = []
        
        # Create more realistic mock jobs based on search terms
        job_titles = [
            f"Senior {search_terms.split()[0]} Developer",
            f"{search_terms.split()[0]} Engineer",
            f"Full Stack {search_terms.split()[0]} Developer",
            f"Lead {search_terms.split()[0]} Specialist",
            f"{search_terms.split()[0]} Software Engineer"
        ]
        
        companies = ["TechCorp", "InnovateLabs", "DataSystems", "CloudTech", "DevSolutions"]
        locations = ["Remote", "San Francisco, CA", "New York, NY", "Austin, TX", "Seattle, WA"]
        
        for i in range(random.randint(3, 8)):
            job = JobApplication(
                title=random.choice(job_titles),
                company=random.choice(companies),
                url=f'{website_url}/job/{i+1}',
                location=random.choice(locations),
                salary=f"${random.randint(70, 150)},000 - ${random.randint(80, 180)},000",
                description=f"Looking for experienced {search_terms} professional with strong technical skills"
            )
            jobs.append(job)
        
        return jobs
    
    def _check_application_success(self, result) -> bool:
        """Enhanced check for application success"""
        # Simulate more realistic success rate with verification challenges
        return random.random() > 0.5  # 50% success rate due to verification challenges

# Update the async wrapper
class AsyncJobAutomationManager:
    """Enhanced wrapper for Flask integration"""
    
    def __init__(self):
        self.automation = EnhancedBrowserJobAutomation()
    
    def run_automation_sync(self, user_profile: Dict, websites: List[Dict], preferences: Dict) -> Dict:
        """Synchronous wrapper for enhanced async automation"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            results = loop.run_until_complete(
                self.automation.run_full_automation(user_profile, websites, preferences)
            )
            
            return {
                'success': True,
                'total_found': results.total_found,
                'total_applied': results.total_applied,
                'applications': [app.dict() for app in results.applications],
                'errors': results.errors
            }
            
        except Exception as e:
            logger.error(f"❌ Enhanced automation wrapper error: {e}")
            return {
                'success': False,
                'error': str(e),
                'total_found': 0,
                'total_applied': 0,
                'applications': [],
                'errors': [str(e)]
            }
        finally:
            try:
                loop.close()
            except:
                pass
