from models import db, User, Resume, Job, Application, JobWebsite
from datetime import datetime, timedelta
import os

class DatabaseManager:
    """Database management utilities"""
    
    @staticmethod
    def get_user_data():
        """Get user data in the old format for compatibility"""
        user = User.get_or_create_default()
        
        # Convert to old format
        preferences = user.preferences or {}
        return {
            'full_name': user.full_name or 'Demo User',
            'email': user.email,
            'phone': user.phone or '+92-123-4567890',
            'skills': preferences.get('skills', 'Python, Web Scraping, Flask, Automation'),
            'experience': preferences.get('experience', '2+ years in Python and automation scripting'),
            'preferred_titles': preferences.get('preferred_titles', 'Python Developer, Web Scraper'),
            'locations': preferences.get('locations', 'Remote, Karachi')
        }
    
    @staticmethod
    def update_user_data(data):
        """Update user data from form"""
        user = User.get_or_create_default()
        
        # Update basic info
        user.full_name = data.get('full_name', user.full_name)
        user.email = data.get('email', user.email)
        user.phone = data.get('phone', user.phone)
        user.location = data.get('locations', user.location)
        
        # Update preferences
        user.update_preferences(
            skills=data.get('skills', ''),
            experience=data.get('experience', ''),
            preferred_titles=data.get('preferred_titles', ''),
            locations=data.get('locations', '')
        )
        
        return user
    
    @staticmethod
    def get_job_websites():
        """Get job websites in old format"""
        user = User.get_or_create_default()
        websites = JobWebsite.query.filter_by(user_id=user.id).all()
        
        # Create defaults if none exist
        if not websites:
            websites = JobWebsite.create_defaults_for_user(user.id)
        
        # Convert to old format
        return [
            {
                'name': w.name,
                'url': w.url,
                'active': w.active,
                'icon': w.icon
            }
            for w in websites
        ]
    
    @staticmethod
    def toggle_website(website_name):
        """Toggle website active status"""
        user = User.get_or_create_default()
        website = JobWebsite.query.filter_by(
            user_id=user.id, 
            name=website_name
        ).first()
        
        if website:
            return website.toggle_active()
        return False
    
    @staticmethod
    def add_website(name, url):
        """Add new website"""
        user = User.get_or_create_default()
        website = JobWebsite(
            user_id=user.id,
            name=name,
            url=url,
            active=True
        )
        db.session.add(website)
        db.session.commit()
        return website
    
    @staticmethod
    def get_applied_jobs():
        """Get applied jobs in old format"""
        user = User.get_or_create_default()
        applications = Application.query.filter_by(user_id=user.id)\
                                      .order_by(Application.applied_at.desc()).all()
        
        # Convert to old format
        return [
            {
                'title': app.job.title if app.job else 'Unknown',
                'company': app.job.company if app.job else 'Unknown',
                'link': app.job.job_url if app.job else '#',
                'date': app.applied_at.strftime('%Y-%m-%d') if app.applied_at else '',
                'status': app.status.title(),
                'website': app.job.website.name if app.job and app.job.website else 'Unknown'
            }
            for app in applications
        ]
    
    @staticmethod
    def simulate_job_applications():
        """Create mock job applications for testing"""
        user = User.get_or_create_default()
        websites = JobWebsite.query.filter_by(user_id=user.id).all()
        
        if not websites:
            websites = JobWebsite.create_defaults_for_user(user.id)
        
        mock_jobs_data = [
            {
                'title': 'Senior Python Developer',
                'company': 'TechCorp Solutions',
                'job_url': 'https://remoteok.com/job/python-dev-123',
                'location': 'Remote',
                'description': 'Looking for experienced Python developer...',
                'website_name': 'RemoteOK'
            },
            {
                'title': 'Web Scraping Specialist',
                'company': 'Data Insights Ltd',
                'job_url': 'https://weworkremotely.com/job/scraper-456',
                'location': 'Remote',
                'description': 'Expert in web scraping and automation...',
                'website_name': 'We Work Remotely'
            },
            {
                'title': 'Flask Developer',
                'company': 'StartupXYZ',
                'job_url': 'https://angel.co/job/flask-789',
                'location': 'Remote',
                'description': 'Full-stack Flask developer needed...',
                'website_name': 'AngelList'
            }
        ]
        
        created_applications = []
        
        for job_data in mock_jobs_data:
            # Find matching website
            website = next((w for w in websites if w.name == job_data['website_name']), websites[0])
            
            # Create job
            job = Job.create_from_scrape(
                user_id=user.id,
                website_id=website.id,
                job_data=job_data
            )
            
            # Create application
            application = Application.create_from_automation(
                user_id=user.id,
                job_id=job.id,
                form_data={'automated': True},
                automation_log=[
                    {
                        'step': 'job_found',
                        'success': True,
                        'details': f'Found job on {website.name}',
                        'timestamp': datetime.utcnow().isoformat()
                    },
                    {
                        'step': 'form_filled',
                        'success': True,
                        'details': 'Application form filled automatically',
                        'timestamp': datetime.utcnow().isoformat()
                    }
                ]
            )
            
            created_applications.append(application)
        
        return created_applications
    
    @staticmethod
    def get_dashboard_stats():
        """Get dashboard statistics"""
        user = User.get_or_create_default()
        
        stats = {
            'total_jobs': Job.query.filter_by(user_id=user.id).count(),
            'total_applications': Application.query.filter_by(user_id=user.id).count(),
            'active_websites': JobWebsite.query.filter_by(user_id=user.id, active=True).count(),
            'recent_applications': Application.query.filter_by(user_id=user.id)\
                                                  .filter(Application.applied_at >= datetime.utcnow() - timedelta(days=7))\
                                                  .count()
        }
        
        return stats
    
    @staticmethod
    def cleanup_old_data(days=30):
        """Clean up old data"""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Delete old jobs without applications
        old_jobs = Job.query.filter(
            Job.found_at < cutoff_date,
            ~Job.applications.any()
        ).all()
        
        for job in old_jobs:
            db.session.delete(job)
        
        db.session.commit()
        return len(old_jobs)
