<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}AI Job Application Assistant{% endblock %}</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            color: #334155;
            line-height: 1.5;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 24px;
        }

        .logo {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
        }

        .logo-icon {
            width: 36px;
            height: 36px;
            background: #4f46e5;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .logo-text {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
        }

        .subtitle {
            color: #64748b;
            font-size: 14px;
            max-width: 500px;
            margin: 0 auto;
        }

        .nav-tabs {
            display: flex;
            justify-content: center;
            gap: 4px;
            margin-bottom: 24px;
            background: rgba(255, 255, 255, 0.8);
            padding: 4px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(226, 232, 240, 0.8);
            width: fit-content;
            margin-left: auto;
            margin-right: auto;
        }

        .nav-tab {
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            color: #64748b;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .nav-tab.active {
            background: #4f46e5;
            color: white;
        }

        .nav-tab:hover:not(.active) {
            color: #334155;
            background: rgba(248, 250, 252, 0.8);
        }

        .main-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            border: 1px solid #e2e8f0;
            margin-bottom: 16px;
        }

        .card-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .card-subtitle {
            color: #64748b;
            font-size: 14px;
        }

        .alert {
            padding: 10px 12px;
            border-radius: 6px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
        }

        .alert-success {
            background: #f0fdf4;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        .alert-error {
            background: #fef2f2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            cursor: pointer;
            border: none;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #4f46e5;
            color: white;
        }

        .btn-primary:hover {
            background: #4338ca;
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
            border: 1px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .form-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 4px;
            font-size: 14px;
        }

        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .mb-16 { margin-bottom: 16px; }
        .mb-20 { margin-bottom: 20px; }
        .mb-24 { margin-bottom: 24px; }

        .text-center { text-align: center; }
        .text-right { text-align: right; }

        @media (max-width: 768px) {
            .grid-2 {
                grid-template-columns: 1fr;
            }
            
            .nav-tabs {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <div class="logo-icon">
                    <i class="fas fa-briefcase"></i>
                </div>
                <span class="logo-text">AI Job Application Assistant ✨</span>
            </div>
            <p class="subtitle">
                Upload your resume, customize your profile, configure job websites, and let AI automate your job applications seamlessly
            </p>
        </div>

        <div class="nav-tabs">
            {% if current_user.is_authenticated %}
                <a href="{{ url_for('upload') }}" class="nav-tab {% if current_page == 'upload' %}active{% endif %}">
                    <i class="fas fa-upload"></i>
                    Upload
                </a>
                <a href="{{ url_for('profile') }}" class="nav-tab {% if current_page == 'profile' %}active{% endif %}">
                    <i class="fas fa-user"></i>
                    Profile
                </a>
                <a href="{{ url_for('websites') }}" class="nav-tab {% if current_page == 'websites' %}active{% endif %}">
                    <i class="fas fa-globe"></i>
                    Websites
                </a>
                <a href="{{ url_for('automate') }}" class="nav-tab {% if current_page == 'automate' %}active{% endif %}">
                    <i class="fas fa-robot"></i>
                    Automate
                </a>
                <a href="{{ url_for('jobs') }}" class="nav-tab {% if current_page == 'jobs' %}active{% endif %}">
                    <i class="fas fa-list"></i>
                    Jobs ({{ jobs|length if jobs else 0 }})
                </a>
                <a href="{{ url_for('logout') }}" class="nav-tab">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            {% else %}
                <a href="{{ url_for('login') }}" class="nav-tab">
                    <i class="fas fa-sign-in-alt"></i>
                    Login
                </a>
                <a href="{{ url_for('signup') }}" class="nav-tab">
                    <i class="fas fa-user-plus"></i>
                    Sign Up
                </a>
            {% endif %}
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'success' if category == 'success' else 'error' }}">
                        <i class="fas fa-{{ 'check-circle' if category == 'success' else 'exclamation-triangle' }}"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>
</body>
</html>
