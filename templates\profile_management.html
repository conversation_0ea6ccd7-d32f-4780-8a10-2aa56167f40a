<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Management - AI Job Application Assistant</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .profile-card { @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6; }
        .field-group { @apply mb-4; }
        .field-label { @apply block text-sm font-medium text-gray-700 mb-1; }
        .field-input { @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500; }
        .field-textarea { @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical; }
        .btn-primary { @apply bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors; }
        .btn-success { @apply bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors; }
        .status-badge { @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium; }
        .status-parsed { @apply bg-green-100 text-green-800; }
        .status-pending { @apply bg-yellow-100 text-yellow-800; }
        .status-failed { @apply bg-red-100 text-red-800; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">AI Job Application Assistant</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{{ url_for('upload') }}" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Upload</a>
                    <a href="{{ url_for('profile_management') }}" class="bg-blue-100 text-blue-700 px-3 py-2 rounded-md text-sm font-medium">Profile</a>
                    <a href="{{ url_for('websites') }}" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Websites</a>
                    <a href="{{ url_for('automate') }}" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Automate</a>
                    <a href="{{ url_for('jobs') }}" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Jobs</a>
                    <a href="{{ url_for('logout') }}" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Logout</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Profile Management</h1>
            <p class="mt-2 text-gray-600">Manage your account information and resume-based profile data</p>
        </div>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="mb-6">
                    {% for category, message in messages %}
                        <div class="alert bg-{{ 'green' if category == 'success' else 'red' if category == 'error' else 'yellow' if category == 'warning' else 'blue' }}-100 
                                    border border-{{ 'green' if category == 'success' else 'red' if category == 'error' else 'yellow' if category == 'warning' else 'blue' }}-400 
                                    text-{{ 'green' if category == 'success' else 'red' if category == 'error' else 'yellow' if category == 'warning' else 'blue' }}-700 
                                    px-4 py-3 rounded mb-4">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Website Account Profile -->
            <div class="profile-card">
                <div class="flex items-center mb-6">
                    <i class="fas fa-user-cog text-2xl text-blue-500 mr-3"></i>
                    <div>
                        <h2 class="text-xl font-semibold text-gray-900">Website Account Profile</h2>
                        <p class="text-sm text-gray-600">Your login credentials and account settings</p>
                    </div>
                </div>

                <form method="POST" action="{{ url_for('update_account_profile') }}">
                    <div class="field-group">
                        <label class="field-label">Email (Login)</label>
                        <input type="email" value="{{ current_user.email }}" 
                               class="field-input bg-gray-100" readonly>
                        <p class="text-xs text-gray-500 mt-1">Email cannot be changed after registration</p>
                    </div>

                    <div class="field-group">
                        <label class="field-label">Full Name</label>
                        <input type="text" name="full_name" value="{{ current_user.full_name or '' }}" 
                               class="field-input" placeholder="Enter your full name">
                    </div>

                    <div class="field-group">
                        <label class="field-label">Phone Number</label>
                        <input type="tel" name="phone" value="{{ current_user.phone or '' }}" 
                               class="field-input" placeholder="Enter your phone number">
                    </div>

                    <div class="field-group">
                        <label class="field-label">Location</label>
                        <input type="text" name="location" value="{{ current_user.location or '' }}" 
                               class="field-input" placeholder="e.g., San Francisco, CA">
                    </div>

                    <button type="submit" class="btn-primary w-full">
                        <i class="fas fa-save mr-2"></i>
                        Update Account Profile
                    </button>
                </form>
            </div>

            <!-- Resume-Based Profile -->
            <div class="profile-card">
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-file-alt text-2xl text-green-500 mr-3"></i>
                        <div>
                            <h2 class="text-xl font-semibold text-gray-900">Resume-Based Profile</h2>
                            <p class="text-sm text-gray-600">Information extracted from your uploaded resume</p>
                        </div>
                    </div>
                    {% if job_profile and job_profile.last_updated_from_resume %}
                        <span class="status-badge status-parsed">
                            <i class="fas fa-check mr-1"></i>Parsed
                        </span>
                    {% else %}
                        <span class="status-badge status-pending">
                            <i class="fas fa-clock mr-1"></i>Not Parsed
                        </span>
                    {% endif %}
                </div>

                <!-- Resume Status -->
                {% if latest_resume %}
                <div class="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="font-medium text-blue-900">Latest Resume</p>
                            <p class="text-sm text-blue-700">{{ latest_resume.filename }}</p>
                            <p class="text-xs text-blue-600">Uploaded: {{ latest_resume.uploaded_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        </div>
                        <div>
                            {% if latest_resume.parsing_status == 'completed' %}
                                <span class="status-badge status-parsed">
                                    <i class="fas fa-check mr-1"></i>Parsed
                                </span>
                            {% elif latest_resume.parsing_status == 'processing' %}
                                <span class="status-badge status-pending">
                                    <i class="fas fa-spinner fa-spin mr-1"></i>Processing
                                </span>
                            {% else %}
                                <span class="status-badge status-pending">
                                    <i class="fas fa-clock mr-1"></i>Pending
                                </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}

                <form method="POST" action="{{ url_for('update_job_profile_simple') }}">
                    <div class="field-group">
                        <label class="field-label">Technical Skills</label>
                        <textarea name="technical_skills" rows="3" class="field-textarea" 
                                  placeholder="Python, JavaScript, React, etc.">{{ job_profile.technical_skills_display if job_profile else current_user.skills or '' }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">Comma-separated list of your technical skills</p>
                    </div>

                    <div class="field-group">
                        <label class="field-label">Experience Summary</label>
                        <textarea name="experience_summary" rows="4" class="field-textarea" 
                                  placeholder="Brief summary of your professional experience">{{ job_profile.professional_summary if job_profile else current_user.experience or '' }}</textarea>
                    </div>

                    <div class="field-group">
                        <label class="field-label">Preferred Job Titles</label>
                        <textarea name="preferred_job_titles" rows="2" class="field-textarea" 
                                  placeholder="Software Engineer, Developer, etc.">{{ job_profile.preferred_job_titles_display if job_profile else current_user.preferred_titles or '' }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">Comma-separated list of job titles you're interested in</p>
                    </div>

                    <div class="field-group">
                        <label class="field-label">Preferred Locations</label>
                        <textarea name="preferred_locations" rows="2" class="field-textarea" 
                                  placeholder="Remote, San Francisco, New York, etc.">{{ job_profile.preferred_locations_display if job_profile else current_user.preferred_locations or '' }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">Comma-separated list of preferred work locations</p>
                    </div>

                    <button type="submit" class="btn-success w-full">
                        <i class="fas fa-save mr-2"></i>
                        Update Resume Profile
                    </button>
                </form>

                <!-- Upload New Resume -->
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <a href="{{ url_for('upload') }}" class="inline-flex items-center text-blue-600 hover:text-blue-800 text-sm font-medium">
                        <i class="fas fa-upload mr-2"></i>
                        Upload New Resume to Auto-Update Profile
                    </a>
                </div>
            </div>
        </div>

        <!-- Account Statistics -->
        <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <i class="fas fa-chart-bar mr-2 text-purple-500"></i>
                Account Statistics
            </h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">{{ current_user.login_count or 0 }}</div>
                    <div class="text-sm text-gray-500">Total Logins</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">{{ resumes_count or 0 }}</div>
                    <div class="text-sm text-gray-500">Resumes Uploaded</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">{{ applications_count or 0 }}</div>
                    <div class="text-sm text-gray-500">Applications</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-orange-600">{{ websites_count or 0 }}</div>
                    <div class="text-sm text-gray-500">Active Websites</div>
                </div>
            </div>
            
            <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                <div>
                    <strong>Member Since:</strong> {{ current_user.created_at.strftime('%B %d, %Y') if current_user.created_at else 'Unknown' }}
                </div>
                <div>
                    <strong>Last Login:</strong> {{ current_user.last_login.strftime('%B %d, %Y at %I:%M %p') if current_user.last_login else 'Never' }}
                </div>
            </div>
        </div>

        <!-- Password Change Section -->
        <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <i class="fas fa-shield-alt mr-2 text-red-500"></i>
                Change Password
            </h3>
            <form method="POST" action="{{ url_for('change_password') }}" class="max-w-md">
                <div class="grid grid-cols-1 gap-4">
                    <div class="field-group">
                        <label class="field-label">Current Password</label>
                        <input type="password" name="current_password" class="field-input" required>
                    </div>
                    <div class="field-group">
                        <label class="field-label">New Password</label>
                        <input type="password" name="new_password" class="field-input" required>
                    </div>
                    <div class="field-group">
                        <label class="field-label">Confirm New Password</label>
                        <input type="password" name="confirm_password" class="field-input" required>
                    </div>
                </div>
                <button type="submit" class="mt-4 bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                    <i class="fas fa-key mr-2"></i>
                    Change Password
                </button>
            </form>
        </div>
    </div>

    <script>
        // Auto-refresh if resume is being processed
        {% if latest_resume and latest_resume.parsing_status == 'processing' %}
            setTimeout(function() {
                location.reload();
            }, 10000); // Refresh every 10 seconds
        {% endif %}
    </script>
</body>
</html>
