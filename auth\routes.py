from flask import Blueprint, render_template, request, redirect, url_for, flash, session
from flask_login import login_user, logout_user, login_required, current_user
from models.auth import User, UserProfile
from models.database import db
from models.website import JobWebsite
from datetime import datetime
import re

auth_bp = Blueprint('auth', __name__)

def is_valid_email(email):
    """Validate email format"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def is_strong_password(password):
    """Check if password is strong enough"""
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"
    if not re.search(r'[A-Z]', password):
        return False, "Password must contain at least one uppercase letter"
    if not re.search(r'[a-z]', password):
        return False, "Password must contain at least one lowercase letter"
    if not re.search(r'\d', password):
        return False, "Password must contain at least one number"
    return True, "Password is strong"

@auth_bp.route('/signup', methods=['GET', 'POST'])
def signup():
    """User registration"""
    if current_user.is_authenticated:
        return redirect(url_for('upload'))
    
    if request.method == 'POST':
        email = request.form.get('email', '').strip().lower()
        password = request.form.get('password', '')
        confirm_password = request.form.get('confirm_password', '')
        full_name = request.form.get('full_name', '').strip()
        
        # Validation
        if not email or not password or not full_name:
            flash('All fields are required', 'error')
            return render_template('auth/signup.html')
        
        if not is_valid_email(email):
            flash('Please enter a valid email address', 'error')
            return render_template('auth/signup.html')
        
        if password != confirm_password:
            flash('Passwords do not match', 'error')
            return render_template('auth/signup.html')
        
        is_strong, message = is_strong_password(password)
        if not is_strong:
            flash(message, 'error')
            return render_template('auth/signup.html')
        
        # Check if user already exists
        if User.query.filter_by(email=email).first():
            flash('Email address already registered', 'error')
            return render_template('auth/signup.html')
        
        try:
            # Create new user
            user = User(
                email=email,
                full_name=full_name
            )
            user.set_password(password)
            user.generate_verification_token()
            
            db.session.add(user)
            db.session.commit()
            
            # Create user profile
            profile = UserProfile.create_for_user(user.id)
            
            # Create default job websites for user
            JobWebsite.create_defaults_for_user(user.id)
            
            # Log in the user
            login_user(user)
            user.record_login()
            
            flash('Account created successfully! Welcome to AI Job Application Assistant.', 'success')
            return redirect(url_for('upload'))
            
        except Exception as e:
            db.session.rollback()
            flash('An error occurred while creating your account. Please try again.', 'error')
            return render_template('auth/signup.html')
    
    return render_template('auth/signup.html')

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """User login"""
    if current_user.is_authenticated:
        return redirect(url_for('upload'))
    
    if request.method == 'POST':
        email = request.form.get('email', '').strip().lower()
        password = request.form.get('password', '')
        remember = bool(request.form.get('remember'))
        
        if not email or not password:
            flash('Email and password are required', 'error')
            return render_template('auth/login.html')
        
        # Find user
        user = User.query.filter_by(email=email).first()
        
        if user and user.check_password(password):
            if not user.is_active:
                flash('Your account has been deactivated. Please contact support.', 'error')
                return render_template('auth/login.html')
            
            login_user(user, remember=remember)
            user.record_login()
            
            # Redirect to next page or dashboard
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            
            flash(f'Welcome back, {user.full_name}!', 'success')
            return redirect(url_for('upload'))
        else:
            flash('Invalid email or password', 'error')
    
    return render_template('auth/login.html')

@auth_bp.route('/logout')
@login_required
def logout():
    """User logout"""
    logout_user()
    flash('You have been logged out successfully', 'info')
    return redirect(url_for('auth.login'))

@auth_bp.route('/profile')
@login_required
def profile():
    """User profile page"""
    user_profile = current_user.profile
    if not user_profile:
        user_profile = UserProfile.create_for_user(current_user.id)
    
    return render_template('auth/profile.html', 
                         user=current_user, 
                         profile=user_profile)

@auth_bp.route('/update-profile', methods=['POST'])
@login_required
def update_profile():
    """Update user profile"""
    try:
        # Update user basic info
        current_user.full_name = request.form.get('full_name', current_user.full_name)
        current_user.phone = request.form.get('phone', current_user.phone)
        current_user.location = request.form.get('location', current_user.location)
        
        # Get or create profile
        profile = current_user.profile
        if not profile:
            profile = UserProfile.create_for_user(current_user.id)
        
        # Update profile information
        profile.skills = request.form.get('skills', profile.skills)
        profile.experience_summary = request.form.get('experience_summary', profile.experience_summary)
        profile.preferred_job_titles = request.form.get('preferred_job_titles', profile.preferred_job_titles)
        profile.preferred_locations = request.form.get('preferred_locations', profile.preferred_locations)
        profile.current_position = request.form.get('current_position', profile.current_position)
        profile.current_company = request.form.get('current_company', profile.current_company)
        
        # Handle numeric fields
        try:
            years_exp = request.form.get('years_of_experience')
            if years_exp:
                profile.years_of_experience = int(years_exp)
        except ValueError:
            pass
        
        try:
            salary_min = request.form.get('salary_expectation_min')
            if salary_min:
                profile.salary_expectation_min = int(salary_min)
        except ValueError:
            pass
        
        try:
            salary_max = request.form.get('salary_expectation_max')
            if salary_max:
                profile.salary_expectation_max = int(salary_max)
        except ValueError:
            pass
        
        # Update preferences
        profile.job_type_preference = request.form.get('job_type_preference', profile.job_type_preference)
        profile.remote_preference = request.form.get('remote_preference', profile.remote_preference)
        profile.education_level = request.form.get('education_level', profile.education_level)
        
        # Update social links
        profile.linkedin_url = request.form.get('linkedin_url', profile.linkedin_url)
        profile.github_url = request.form.get('github_url', profile.github_url)
        profile.portfolio_url = request.form.get('portfolio_url', profile.portfolio_url)
        
        profile.updated_at = datetime.utcnow()
        db.session.commit()
        
        flash('Profile updated successfully!', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash('An error occurred while updating your profile', 'error')
    
    return redirect(url_for('auth.profile'))

@auth_bp.route('/change-password', methods=['POST'])
@login_required
def change_password():
    """Change user password"""
    current_password = request.form.get('current_password', '')
    new_password = request.form.get('new_password', '')
    confirm_password = request.form.get('confirm_password', '')
    
    if not current_password or not new_password or not confirm_password:
        flash('All password fields are required', 'error')
        return redirect(url_for('auth.profile'))
    
    if not current_user.check_password(current_password):
        flash('Current password is incorrect', 'error')
        return redirect(url_for('auth.profile'))
    
    if new_password != confirm_password:
        flash('New passwords do not match', 'error')
        return redirect(url_for('auth.profile'))
    
    is_strong, message = is_strong_password(new_password)
    if not is_strong:
        flash(message, 'error')
        return redirect(url_for('auth.profile'))
    
    try:
        current_user.set_password(new_password)
        db.session.commit()
        flash('Password changed successfully!', 'success')
    except Exception as e:
        db.session.rollback()
        flash('An error occurred while changing your password', 'error')
    
    return redirect(url_for('auth.profile'))

@auth_bp.route('/delete-account', methods=['POST'])
@login_required
def delete_account():
    """Delete user account"""
    password = request.form.get('password', '')
    
    if not password:
        flash('Password is required to delete account', 'error')
        return redirect(url_for('auth.profile'))
    
    if not current_user.check_password(password):
        flash('Password is incorrect', 'error')
        return redirect(url_for('auth.profile'))
    
    try:
        # Delete user (cascade will handle related data)
        db.session.delete(current_user)
        db.session.commit()
        
        logout_user()
        flash('Your account has been deleted successfully', 'info')
        return redirect(url_for('auth.signup'))
        
    except Exception as e:
        db.session.rollback()
        flash('An error occurred while deleting your account', 'error')
        return redirect(url_for('auth.profile'))
