"""
Supabase database operations and utilities
"""
from typing import Dict, List, Optional, Any
from datetime import datetime
import uuid
import json
import logging
from .supabase_config import get_supabase_client

logger = logging.getLogger(__name__)

class SupabaseModel:
    """Base class for Supabase models"""
    
    table_name: str = ""
    
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    @classmethod
    def get_client(cls):
        """Get Supabase client"""
        return get_supabase_client()
    
    @classmethod
    def create(cls, data: Dict[str, Any]) -> Optional['SupabaseModel']:
        """Create a new record"""
        try:
            # Add timestamps
            now = datetime.utcnow().isoformat()
            data['created_at'] = now
            data['updated_at'] = now
            
            # Generate UUID if not provided
            if 'id' not in data:
                data['id'] = str(uuid.uuid4())
            
            result = cls.get_client().table(cls.table_name).insert(data).execute()
            
            if result.data:
                return cls(**result.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error creating {cls.__name__}: {e}")
            return None
    
    @classmethod
    def get_by_id(cls, record_id: str) -> Optional['SupabaseModel']:
        """Get record by ID"""
        try:
            result = cls.get_client().table(cls.table_name).select("*").eq('id', record_id).execute()
            
            if result.data:
                return cls(**result.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error getting {cls.__name__} by ID: {e}")
            return None
    
    @classmethod
    def get_all(cls, filters: Optional[Dict[str, Any]] = None) -> List['SupabaseModel']:
        """Get all records with optional filters"""
        try:
            query = cls.get_client().table(cls.table_name).select("*")
            
            if filters:
                for key, value in filters.items():
                    query = query.eq(key, value)
            
            result = query.execute()
            
            return [cls(**record) for record in result.data]
            
        except Exception as e:
            logger.error(f"Error getting all {cls.__name__}: {e}")
            return []
    
    @classmethod
    def filter_by(cls, **kwargs) -> List['SupabaseModel']:
        """Filter records by given criteria"""
        return cls.get_all(filters=kwargs)
    
    def update(self, data: Dict[str, Any]) -> bool:
        """Update this record"""
        try:
            # Add updated timestamp
            data['updated_at'] = datetime.utcnow().isoformat()
            
            result = self.get_client().table(self.table_name).update(data).eq('id', self.id).execute()
            
            if result.data:
                # Update local attributes
                for key, value in data.items():
                    setattr(self, key, value)
                return True
            return False
            
        except Exception as e:
            logger.error(f"Error updating {self.__class__.__name__}: {e}")
            return False
    
    def delete(self) -> bool:
        """Delete this record"""
        try:
            result = self.get_client().table(self.table_name).delete().eq('id', self.id).execute()
            return True
            
        except Exception as e:
            logger.error(f"Error deleting {self.__class__.__name__}: {e}")
            return False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary"""
        result = {}
        for key, value in self.__dict__.items():
            if not key.startswith('_'):
                if isinstance(value, datetime):
                    result[key] = value.isoformat()
                else:
                    result[key] = value
        return result

def init_supabase_db(app=None):
    """Initialize Supabase database"""
    try:
        client = get_supabase_client()
        
        if app:
            app.config['SUPABASE_CLIENT'] = client
        
        logger.info("✅ Supabase database initialized successfully")
        return client
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize Supabase database: {e}")
        return None

def get_db_stats() -> Dict[str, int]:
    """Get database statistics"""
    try:
        client = get_supabase_client()
        stats = {}
        
        tables = ['users', 'resumes', 'jobs', 'applications', 'job_websites']
        
        for table in tables:
            try:
                result = client.table(table).select('id', count='exact').execute()
                stats[table] = result.count if result.count is not None else 0
            except Exception as e:
                logger.warning(f"Could not get count for table {table}: {e}")
                stats[table] = 0
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting database stats: {e}")
        return {}
