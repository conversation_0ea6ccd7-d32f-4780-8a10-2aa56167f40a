"""
Create an admin user for testing
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask
from models import init_db
from models.auth import User, UserProfile
from models.website import JobWebsite

def create_admin_user():
    """Create admin user with sample data"""
    
    print("🔧 Creating Admin User")
    print("=" * 30)
    
    app = Flask(__name__)
    init_db(app)
    
    with app.app_context():
        # Check if admin exists
        admin = User.query.filter_by(email='<EMAIL>').first()
        
        if admin:
            print("❌ Admin user already exists!")
            print(f"   Email: {admin.email}")
            return
        
        # Create admin user
        admin = User(
            email='<EMAIL>',
            full_name='Admin User',
            phone='******-0123',
            location='San Francisco, CA',
            is_active=True,
            email_verified=True
        )
        admin.set_password('Admin123!')
        
        from models.database import db
        db.session.add(admin)
        db.session.commit()
        
        # Create profile
        profile = UserProfile(
            user_id=admin.id,
            skills='Python, Flask, JavaScript, React, SQL, Docker',
            experience_summary='5+ years of full-stack development experience',
            preferred_job_titles='Senior Developer, Tech Lead, Full Stack Engineer',
            preferred_locations='Remote, San Francisco, New York',
            current_position='Senior Software Engineer',
            current_company='Tech Startup Inc.',
            years_of_experience=5,
            education_level='bachelor',
            salary_expectation_min=80000,
            salary_expectation_max=120000,
            job_type_preference='full-time',
            remote_preference='remote',
            linkedin_url='https://linkedin.com/in/admin',
            github_url='https://github.com/admin'
        )
        db.session.add(profile)
        db.session.commit()
        
        # Create default websites
        JobWebsite.create_defaults_for_user(admin.id)
        
        print("✅ Admin user created successfully!")
        print(f"   Email: {admin.email}")
        print(f"   Password: Admin123!")
        print(f"   Name: {admin.full_name}")
        print(f"   ID: {admin.id}")
        print()
        print("🔐 Login credentials:")
        print("   Email: <EMAIL>")
        print("   Password: Admin123!")

if __name__ == "__main__":
    create_admin_user()
