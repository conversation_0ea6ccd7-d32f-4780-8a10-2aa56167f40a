from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import os

db = SQLAlchemy()

def init_db(app):
    """Initialize database with Flask app"""
    
    # Database configuration
    basedir = os.path.abspath(os.path.dirname(__file__))
    database_path = os.path.join(basedir, '..', 'instance', 'jobapp.db')
    
    # Ensure instance directory exists
    instance_dir = os.path.dirname(database_path)
    os.makedirs(instance_dir, exist_ok=True)
    
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{database_path}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    db.init_app(app)
    
    with app.app_context():
        # Create all tables
        db.create_all()
        
        print("✅ Database initialized successfully")
        print(f"📁 Database file: {database_path}")
    
    return db

def get_db_stats():
    """Get database statistics"""
    from .user import User
    from .resume import Resume
    from .job import Job
    from .application import Application
    from .website import JobWebsite
    
    stats = {
        'users': User.query.count(),
        'resumes': Resume.query.count(),
        'jobs': Job.query.count(),
        'applications': Application.query.count(),
        'websites': JobWebsite.query.count()
    }
    return stats
