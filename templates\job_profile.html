<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Profile - AI Job Application Assistant</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .section-card { @apply bg-white rounded-lg shadow-sm border border-gray-200 mb-6; }
        .section-header { @apply px-6 py-4 border-b border-gray-200 bg-gray-50; }
        .section-content { @apply px-6 py-4; }
        .field-group { @apply mb-4; }
        .field-label { @apply block text-sm font-medium text-gray-700 mb-1; }
        .field-input { @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500; }
        .field-textarea { @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical; }
        .field-select { @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500; }
        .grid-2 { @apply grid grid-cols-1 md:grid-cols-2 gap-4; }
        .grid-3 { @apply grid grid-cols-1 md:grid-cols-3 gap-4; }
        .btn-primary { @apply bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2; }
        .btn-secondary { @apply bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2; }
        .status-badge { @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium; }
        .status-parsed { @apply bg-green-100 text-green-800; }
        .status-pending { @apply bg-yellow-100 text-yellow-800; }
        .status-failed { @apply bg-red-100 text-red-800; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">AI Job Application Assistant</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{{ url_for('upload') }}" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Upload</a>
                    <a href="{{ url_for('job_profile') }}" class="bg-blue-100 text-blue-700 px-3 py-2 rounded-md text-sm font-medium">Job Profile</a>
                    <a href="{{ url_for('account_profile') }}" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Account</a>
                    <a href="{{ url_for('websites') }}" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Websites</a>
                    <a href="{{ url_for('automate') }}" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Automate</a>
                    <a href="{{ url_for('jobs') }}" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Jobs</a>
                    <a href="{{ url_for('logout') }}" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Logout</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="mb-6">
                    {% for category, message in messages %}
                        <div class="alert bg-{{ 'green' if category == 'success' else 'red' if category == 'error' else 'yellow' if category == 'warning' else 'blue' }}-100 
                                    border border-{{ 'green' if category == 'success' else 'red' if category == 'error' else 'yellow' if category == 'warning' else 'blue' }}-400 
                                    text-{{ 'green' if category == 'success' else 'red' if category == 'error' else 'yellow' if category == 'warning' else 'blue' }}-700 
                                    px-4 py-3 rounded mb-4">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Job Profile</h1>
                    <p class="mt-2 text-gray-600">Complete professional profile for job applications</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-right">
                        <div class="text-2xl font-bold text-blue-600">{{ profile.profile_completion_score or 0 }}%</div>
                        <div class="text-sm text-gray-500">Complete</div>
                    </div>
                    <div class="w-16 h-16">
                        <svg class="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                            <path d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" 
                                  fill="none" stroke="#e5e7eb" stroke-width="2"/>
                            <path d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" 
                                  fill="none" stroke="#3b82f6" stroke-width="2" 
                                  stroke-dasharray="{{ (profile.profile_completion_score or 0) }}, 100"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Resume Status -->
        {% if latest_resume %}
        <div class="section-card">
            <div class="section-header">
                <h2 class="text-lg font-medium text-gray-900 flex items-center">
                    <i class="fas fa-file-alt mr-2 text-blue-500"></i>
                    Resume Status
                </h2>
            </div>
            <div class="section-content">
                <div class="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                    <div>
                        <p class="font-medium text-blue-900">{{ latest_resume.filename }}</p>
                        <p class="text-sm text-blue-700">Uploaded: {{ latest_resume.uploaded_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        {% if profile.last_updated_from_resume %}
                        <p class="text-sm text-blue-600">Profile updated: {{ profile.last_updated_from_resume.strftime('%Y-%m-%d %H:%M') }}</p>
                        {% endif %}
                    </div>
                    <div>
                        {% if latest_resume.parsing_status == 'completed' %}
                            <span class="status-badge status-parsed">
                                <i class="fas fa-check mr-1"></i>Parsed
                            </span>
                        {% elif latest_resume.parsing_status == 'processing' %}
                            <span class="status-badge status-pending">
                                <i class="fas fa-spinner fa-spin mr-1"></i>Processing
                            </span>
                        {% else %}
                            <span class="status-badge status-failed">
                                <i class="fas fa-exclamation-triangle mr-1"></i>{{ latest_resume.parsing_status.title() }}
                            </span>
                        {% endif %}
                    </div>
                </div>
                
                {% if latest_resume.parsing_status != 'completed' %}
                <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div class="flex">
                        <i class="fas fa-info-circle text-yellow-400 mt-0.5 mr-3"></i>
                        <div>
                            <h3 class="text-sm font-medium text-yellow-800">Resume Not Fully Processed</h3>
                            <p class="text-sm text-yellow-700 mt-1">
                                Upload a new resume or wait for processing to complete for automatic profile updates.
                            </p>
                            <a href="{{ url_for('upload') }}" class="inline-flex items-center mt-2 text-sm text-yellow-800 hover:text-yellow-900">
                                <i class="fas fa-upload mr-1"></i>Upload New Resume
                            </a>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <form method="POST" action="{{ url_for('update_job_profile') }}" class="space-y-6">
            <!-- Personal Information -->
            <div class="section-card">
                <div class="section-header">
                    <h2 class="text-lg font-medium text-gray-900 flex items-center">
                        <i class="fas fa-user mr-2 text-green-500"></i>
                        Personal Information
                    </h2>
                </div>
                <div class="section-content">
                    <div class="grid-3">
                        <div class="field-group">
                            <label class="field-label">First Name *</label>
                            <input type="text" name="first_name" value="{{ profile.first_name or '' }}" class="field-input" required>
                        </div>
                        <div class="field-group">
                            <label class="field-label">Middle Name</label>
                            <input type="text" name="middle_name" value="{{ profile.middle_name or '' }}" class="field-input">
                        </div>
                        <div class="field-group">
                            <label class="field-label">Last Name *</label>
                            <input type="text" name="last_name" value="{{ profile.last_name or '' }}" class="field-input" required>
                        </div>
                    </div>
                    
                    <div class="grid-2">
                        <div class="field-group">
                            <label class="field-label">Date of Birth</label>
                            <input type="date" name="date_of_birth" value="{{ profile.date_of_birth or '' }}" class="field-input">
                        </div>
                        <div class="field-group">
                            <label class="field-label">Nationality</label>
                            <input type="text" name="nationality" value="{{ profile.nationality or '' }}" class="field-input">
                        </div>
                    </div>
                    
                    <div class="grid-2">
                        <div class="field-group">
                            <label class="field-label">Gender</label>
                            <select name="gender" class="field-select">
                                <option value="">Select Gender</option>
                                <option value="Male" {{ 'selected' if profile.gender == 'Male' }}>Male</option>
                                <option value="Female" {{ 'selected' if profile.gender == 'Female' }}>Female</option>
                                <option value="Other" {{ 'selected' if profile.gender == 'Other' }}>Other</option>
                                <option value="Prefer not to say" {{ 'selected' if profile.gender == 'Prefer not to say' }}>Prefer not to say</option>
                            </select>
                        </div>
                        <div class="field-group">
                            <label class="field-label">Marital Status</label>
                            <select name="marital_status" class="field-select">
                                <option value="">Select Status</option>
                                <option value="Single" {{ 'selected' if profile.marital_status == 'Single' }}>Single</option>
                                <option value="Married" {{ 'selected' if profile.marital_status == 'Married' }}>Married</option>
                                <option value="Divorced" {{ 'selected' if profile.marital_status == 'Divorced' }}>Divorced</option>
                                <option value="Widowed" {{ 'selected' if profile.marital_status == 'Widowed' }}>Widowed</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="section-card">
                <div class="section-header">
                    <h2 class="text-lg font-medium text-gray-900 flex items-center">
                        <i class="fas fa-address-book mr-2 text-blue-500"></i>
                        Contact Information
                    </h2>
                </div>
                <div class="section-content">
                    <div class="grid-2">
                        <div class="field-group">
                            <label class="field-label">Email Address *</label>
                            <input type="email" name="email" value="{{ profile.email or current_user.email }}" class="field-input" required>
                        </div>
                        <div class="field-group">
                            <label class="field-label">Primary Phone *</label>
                            <input type="tel" name="phone_primary" value="{{ profile.phone_primary or '' }}" class="field-input" required>
                        </div>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">Secondary Phone</label>
                        <input type="tel" name="phone_secondary" value="{{ profile.phone_secondary or '' }}" class="field-input">
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">Address Line 1</label>
                        <input type="text" name="address_line1" value="{{ profile.address_line1 or '' }}" class="field-input">
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">Address Line 2</label>
                        <input type="text" name="address_line2" value="{{ profile.address_line2 or '' }}" class="field-input">
                    </div>
                    
                    <div class="grid-3">
                        <div class="field-group">
                            <label class="field-label">City</label>
                            <input type="text" name="city" value="{{ profile.city or '' }}" class="field-input">
                        </div>
                        <div class="field-group">
                            <label class="field-label">State/Province</label>
                            <input type="text" name="state_province" value="{{ profile.state_province or '' }}" class="field-input">
                        </div>
                        <div class="field-group">
                            <label class="field-label">Postal Code</label>
                            <input type="text" name="postal_code" value="{{ profile.postal_code or '' }}" class="field-input">
                        </div>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">Country</label>
                        <input type="text" name="country" value="{{ profile.country or '' }}" class="field-input">
                    </div>
                </div>
            </div>

            <!-- Professional Summary -->
            <div class="section-card">
                <div class="section-header">
                    <h2 class="text-lg font-medium text-gray-900 flex items-center">
                        <i class="fas fa-briefcase mr-2 text-purple-500"></i>
                        Professional Summary
                    </h2>
                </div>
                <div class="section-content">
                    <div class="field-group">
                        <label class="field-label">Professional Summary *</label>
                        <textarea name="professional_summary" rows="4" class="field-textarea" required 
                                  placeholder="Write a compelling summary of your professional background, key achievements, and career goals...">{{ profile.professional_summary or '' }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">This will be used as your elevator pitch in applications</p>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">Career Objective</label>
                        <textarea name="career_objective" rows="3" class="field-textarea" 
                                  placeholder="Describe your career goals and what you're looking for in your next role...">{{ profile.career_objective or '' }}</textarea>
                    </div>
                    
                    <div class="grid-2">
                        <div class="field-group">
                            <label class="field-label">Years of Experience</label>
                            <input type="number" name="years_of_experience" value="{{ profile.years_of_experience or '' }}" 
                                   class="field-input" min="0" max="50">
                        </div>
                        <div class="field-group">
                            <label class="field-label">Current Position</label>
                            <input type="text" name="current_position" value="{{ profile.current_position or '' }}" class="field-input">
                        </div>
                    </div>
                    
                    <div class="grid-2">
                        <div class="field-group">
                            <label class="field-label">Current Company</label>
                            <input type="text" name="current_company" value="{{ profile.current_company or '' }}" class="field-input">
                        </div>
                        <div class="field-group">
                            <label class="field-label">Current Salary</label>
                            <input type="text" name="current_salary" value="{{ profile.current_salary or '' }}" 
                                   class="field-input" placeholder="e.g., $75,000 or Confidential">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Skills & Competencies -->
            <div class="section-card">
                <div class="section-header">
                    <h2 class="text-lg font-medium text-gray-900 flex items-center">
                        <i class="fas fa-cogs mr-2 text-orange-500"></i>
                        Skills & Competencies
                    </h2>
                </div>
                <div class="section-content">
                    <div class="grid-2">
                        <div class="field-group">
                            <label class="field-label">Technical Skills *</label>
                            <textarea name="technical_skills" rows="3" class="field-textarea" required
                                      placeholder="Python, JavaScript, React, AWS, Docker...">{{ profile.technical_skills_display or '' }}</textarea>
                            <p class="text-xs text-gray-500 mt-1">Comma-separated list of technical skills</p>
                        </div>
                        <div class="field-group">
                            <label class="field-label">Programming Languages</label>
                            <textarea name="programming_languages" rows="3" class="field-textarea"
                                      placeholder="Python, JavaScript, Java, C++...">{{ profile.programming_languages_display or '' }}</textarea>
                        </div>
                    </div>
                    
                    <div class="grid-2">
                        <div class="field-group">
                            <label class="field-label">Frameworks & Libraries</label>
                            <textarea name="frameworks_libraries" rows="3" class="field-textarea"
                                      placeholder="React, Django, Flask, Spring Boot...">{{ profile.frameworks_libraries_display or '' }}</textarea>
                        </div>
                        <div class="field-group">
                            <label class="field-label">Databases</label>
                            <textarea name="databases" rows="3" class="field-textarea"
                                      placeholder="MySQL, PostgreSQL, MongoDB, Redis...">{{ profile.databases_display or '' }}</textarea>
                        </div>
                    </div>
                    
                    <div class="grid-2">
                        <div class="field-group">
                            <label class="field-label">Tools & Software</label>
                            <textarea name="tools_software" rows="3" class="field-textarea"
                                      placeholder="Git, Docker, Jenkins, Jira...">{{ profile.tools_software_display or '' }}</textarea>
                        </div>
                        <div class="field-group">
                            <label class="field-label">Cloud Platforms</label>
                            <textarea name="cloud_platforms" rows="3" class="field-textarea"
                                      placeholder="AWS, Azure, Google Cloud, Heroku...">{{ profile.cloud_platforms_display or '' }}</textarea>
                        </div>
                    </div>
                    
                    <div class="grid-2">
                        <div class="field-group">
                            <label class="field-label">Operating Systems</label>
                            <textarea name="operating_systems" rows="2" class="field-textarea"
                                      placeholder="Linux, Windows, macOS, Ubuntu...">{{ profile.operating_systems_display or '' }}</textarea>
                        </div>
                        <div class="field-group">
                            <label class="field-label">Methodologies</label>
                            <textarea name="methodologies" rows="2" class="field-textarea"
                                      placeholder="Agile, Scrum, DevOps, TDD...">{{ profile.methodologies_display or '' }}</textarea>
                        </div>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">Soft Skills</label>
                        <textarea name="soft_skills" rows="2" class="field-textarea"
                                  placeholder="Leadership, Communication, Problem Solving, Team Management...">{{ profile.soft_skills_display or '' }}</textarea>
                    </div>
                </div>
            </div>

            <!-- Job Preferences -->
            <div class="section-card">
                <div class="section-header">
                    <h2 class="text-lg font-medium text-gray-900 flex items-center">
                        <i class="fas fa-target mr-2 text-red-500"></i>
                        Job Preferences
                    </h2>
                </div>
                <div class="section-content">
                    <div class="field-group">
                        <label class="field-label">Preferred Job Titles *</label>
                        <textarea name="preferred_job_titles" rows="2" class="field-textarea" required
                                  placeholder="Software Engineer, Full Stack Developer, Senior Developer...">{{ profile.preferred_job_titles_display or '' }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">Comma-separated list of job titles you're interested in</p>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">Preferred Industries</label>
                        <textarea name="preferred_industries" rows="2" class="field-textarea"
                                  placeholder="Technology, Healthcare, Finance, E-commerce...">{{ profile.preferred_industries_display or '' }}</textarea>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">Preferred Locations</label>
                        <textarea name="preferred_locations" rows="2" class="field-textarea"
                                  placeholder="Remote, San Francisco, New York, London...">{{ profile.preferred_locations_display or '' }}</textarea>
                    </div>
                    
                    <div class="grid-2">
                        <div class="field-group">
                            <label class="field-label">Remote Work Preference</label>
                            <select name="remote_work_preference" class="field-select">
                                <option value="">Select Preference</option>
                                <option value="remote" {{ 'selected' if profile.remote_work_preference == 'remote' }}>Remote Only</option>
                                <option value="hybrid" {{ 'selected' if profile.remote_work_preference == 'hybrid' }}>Hybrid</option>
                                <option value="onsite" {{ 'selected' if profile.remote_work_preference == 'onsite' }}>On-site Only</option>
                                <option value="flexible" {{ 'selected' if profile.remote_work_preference == 'flexible' }}>Flexible</option>
                            </select>
                        </div>
                        <div class="field-group">
                            <label class="field-label">Job Type Preference</label>
                            <textarea name="job_type_preference" rows="2" class="field-textarea"
                                      placeholder="Full-time, Part-time, Contract, Freelance...">{{ profile.job_type_preference_display or '' }}</textarea>
                        </div>
                    </div>
                    
                    <div class="grid-3">
                        <div class="field-group">
                            <label class="field-label">Expected Salary (Min)</label>
                            <input type="number" name="expected_salary_min" value="{{ profile.expected_salary_min or '' }}" 
                                   class="field-input" placeholder="50000">
                        </div>
                        <div class="field-group">
                            <label class="field-label">Expected Salary (Max)</label>
                            <input type="number" name="expected_salary_max" value="{{ profile.expected_salary_max or '' }}" 
                                   class="field-input" placeholder="100000">
                        </div>
                        <div class="field-group">
                            <label class="field-label">Currency</label>
                            <select name="salary_currency" class="field-select">
                                <option value="USD" {{ 'selected' if profile.salary_currency == 'USD' }}>USD ($)</option>
                                <option value="EUR" {{ 'selected' if profile.salary_currency == 'EUR' }}>EUR (€)</option>
                                <option value="GBP" {{ 'selected' if profile.salary_currency == 'GBP' }}>GBP (£)</option>
                                <option value="CAD" {{ 'selected' if profile.salary_currency == 'CAD' }}>CAD ($)</option>
                                <option value="AUD" {{ 'selected' if profile.salary_currency == 'AUD' }}>AUD ($)</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="grid-2">
                        <div class="field-group">
                            <label class="field-label">Availability Start Date</label>
                            <input type="date" name="availability_start_date" value="{{ profile.availability_start_date or '' }}" class="field-input">
                        </div>
                        <div class="field-group">
                            <label class="field-label">Willing to Travel</label>
                            <select name="willing_to_travel" class="field-select">
                                <option value="">Select Option</option>
                                <option value="none" {{ 'selected' if profile.willing_to_travel == 'none' }}>No Travel</option>
                                <option value="occasional" {{ 'selected' if profile.willing_to_travel == 'occasional' }}>Occasional (< 25%)</option>
                                <option value="frequent" {{ 'selected' if profile.willing_to_travel == 'frequent' }}>Frequent (25-50%)</option>
                                <option value="extensive" {{ 'selected' if profile.willing_to_travel == 'extensive' }}>Extensive (> 50%)</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="grid-2">
                        <div class="field-group">
                            <label class="field-label">Willing to Relocate</label>
                            <select name="willing_to_relocate" class="field-select">
                                <option value="">Select Option</option>
                                <option value="true" {{ 'selected' if profile.willing_to_relocate }}>Yes</option>
                                <option value="false" {{ 'selected' if profile.willing_to_relocate == False }}>No</option>
                            </select>
                        </div>
                        <div class="field-group">
                            <label class="field-label">Work Authorization</label>
                            <input type="text" name="work_authorization" value="{{ profile.work_authorization or '' }}" 
                                   class="field-input" placeholder="e.g., US Citizen, H1B, Green Card, EU Citizen">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Social & Online Presence -->
            <div class="section-card">
                <div class="section-header">
                    <h2 class="text-lg font-medium text-gray-900 flex items-center">
                        <i class="fas fa-globe mr-2 text-indigo-500"></i>
                        Social & Online Presence
                    </h2>
                </div>
                <div class="section-content">
                    <div class="grid-2">
                        <div class="field-group">
                            <label class="field-label">LinkedIn URL</label>
                            <input type="url" name="linkedin_url" value="{{ profile.linkedin_url or '' }}" 
                                   class="field-input" placeholder="https://linkedin.com/in/yourprofile">
                        </div>
                        <div class="field-group">
                            <label class="field-label">GitHub URL</label>
                            <input type="url" name="github_url" value="{{ profile.github_url or '' }}" 
                                   class="field-input" placeholder="https://github.com/yourusername">
                        </div>
                    </div>
                    
                    <div class="grid-2">
                        <div class="field-group">
                            <label class="field-label">Portfolio URL</label>
                            <input type="url" name="portfolio_url" value="{{ profile.portfolio_url or '' }}" 
                                   class="field-input" placeholder="https://yourportfolio.com">
                        </div>
                        <div class="field-group">
                            <label class="field-label">Personal Website</label>
                            <input type="url" name="personal_website" value="{{ profile.personal_website or '' }}" 
                                   class="field-input" placeholder="https://yourwebsite.com">
                        </div>
                    </div>
                    
                    <div class="grid-2">
                        <div class="field-group">
                            <label class="field-label">Twitter URL</label>
                            <input type="url" name="twitter_url" value="{{ profile.twitter_url or '' }}" 
                                   class="field-input" placeholder="https://twitter.com/yourusername">
                        </div>
                        <div class="field-group">
                            <label class="field-label">Stack Overflow URL</label>
                            <input type="url" name="stackoverflow_url" value="{{ profile.stackoverflow_url or '' }}" 
                                   class="field-input" placeholder="https://stackoverflow.com/users/yourprofile">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Languages -->
            <div class="section-card">
                <div class="section-header">
                    <h2 class="text-lg font-medium text-gray-900 flex items-center">
                        <i class="fas fa-language mr-2 text-pink-500"></i>
                        Languages
                    </h2>
                </div>
                <div class="section-content">
                    <div class="field-group">
                        <label class="field-label">Languages Spoken</label>
                        <textarea name="languages_spoken" rows="3" class="field-textarea"
                                  placeholder="English (Native), Spanish (Fluent), French (Intermediate)...">{{ profile.languages_spoken_display or '' }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">Format: Language (Proficiency Level)</p>
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="section-card">
                <div class="section-header">
                    <h2 class="text-lg font-medium text-gray-900 flex items-center">
                        <i class="fas fa-plus-circle mr-2 text-teal-500"></i>
                        Additional Information
                    </h2>
                </div>
                <div class="section-content">
                    <div class="field-group">
                        <label class="field-label">Hobbies & Interests</label>
                        <textarea name="hobbies_interests" rows="2" class="field-textarea"
                                  placeholder="Photography, Hiking, Open Source Contributions, Reading...">{{ profile.hobbies_interests or '' }}</textarea>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">Additional Information</label>
                        <textarea name="additional_information" rows="3" class="field-textarea"
                                  placeholder="Any other relevant information you'd like to include...">{{ profile.additional_information or '' }}</textarea>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">Cover Letter Template</label>
                        <textarea name="cover_letter_template" rows="6" class="field-textarea"
                                  placeholder="Write a template cover letter that can be customized for different applications...">{{ profile.cover_letter_template or '' }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">This template will be used to generate personalized cover letters</p>
                    </div>
                </div>
            </div>

            <!-- Auto-Update Settings -->
            <div class="section-card">
                <div class="section-header">
                    <h2 class="text-lg font-medium text-gray-900 flex items-center">
                        <i class="fas fa-sync-alt mr-2 text-gray-500"></i>
                        Auto-Update Settings
                    </h2>
                </div>
                <div class="section-content">
                    <div class="flex items-center">
                        <input type="checkbox" name="auto_update_enabled" value="true" 
                               {{ 'checked' if profile.auto_update_enabled }} 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label class="ml-2 block text-sm text-gray-900">
                            Automatically update profile when new resume is uploaded
                        </label>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">When enabled, uploading a new resume will automatically update relevant profile fields</p>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-between items-center pt-6">
                <div class="flex space-x-4">
                    <button type="button" onclick="window.history.back()" class="btn-secondary">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back
                    </button>
                    <a href="{{ url_for('upload') }}" class="btn-secondary">
                        <i class="fas fa-upload mr-2"></i>
                        Upload Resume
                    </a>
                </div>
                
                <div class="flex space-x-4">
                    <button type="button" onclick="saveAsDraft()" class="btn-secondary">
                        <i class="fas fa-save mr-2"></i>
                        Save Draft
                    </button>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-check mr-2"></i>
                        Save Profile
                    </button>
                </div>
            </div>
        </form>
    </div>

    <script>
        // Auto-save functionality
        let autoSaveTimeout;
        
        function autoSave() {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(() => {
                saveAsDraft();
            }, 30000); // Auto-save every 30 seconds
        }
        
        function saveAsDraft() {
            const formData = new FormData(document.querySelector('form'));
            formData.append('save_as_draft', 'true');
            
            fetch('{{ url_for("update_job_profile") }}', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Draft saved successfully', 'success');
                }
            })
            .catch(error => {
                console.error('Auto-save error:', error);
            });
        }
        
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 px-4 py-2 rounded-md text-white z-50 ${
                type === 'success' ? 'bg-green-500' : 'bg-red-500'
            }`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
        
        // Add event listeners for auto-save
        document.querySelectorAll('input, textarea, select').forEach(element => {
            element.addEventListener('input', autoSave);
            element.addEventListener('change', autoSave);
        });
        
        // Auto-refresh if resume is being processed
        {% if latest_resume and latest_resume.parsing_status == 'processing' %}
            setTimeout(function() {
                location.reload();
            }, 15000); // Refresh every 15 seconds
        {% endif %}
        
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const requiredFields = document.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('border-red-500');
                    isValid = false;
                } else {
                    field.classList.remove('border-red-500');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                showNotification('Please fill in all required fields', 'error');
                document.querySelector('[required]:invalid').scrollIntoView();
            }
        });
    </script>
</body>
</html>
