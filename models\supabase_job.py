"""
Supabase-compatible Job model
"""
from typing import Optional, Dict, Any, List
from datetime import datetime
import uuid
import json
import logging
from .supabase_database import SupabaseModel

logger = logging.getLogger(__name__)

class Job(SupabaseModel):
    """Job model for Supabase"""
    
    table_name = "jobs"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Set default values
        self.id = kwargs.get('id', str(uuid.uuid4()))
        self.user_id = kwargs.get('user_id', '')
        self.website_id = kwargs.get('website_id', '')
        
        # Job information
        self.title = kwargs.get('title', '')
        self.company = kwargs.get('company', '')
        self.location = kwargs.get('location', '')
        self.salary_range = kwargs.get('salary_range', '')
        self.job_url = kwargs.get('job_url', '')
        
        # Job details
        self.description = kwargs.get('description', '')
        self.requirements = kwargs.get('requirements', '')
        self.job_type = kwargs.get('job_type', '')
        self.remote_type = kwargs.get('remote_type', '')
        
        # Metadata
        self.external_id = kwargs.get('external_id', '')
        
        # Handle JSON fields
        tags = kwargs.get('tags')
        if isinstance(tags, str):
            try:
                self.tags = json.loads(tags)
            except json.JSONDecodeError:
                self.tags = []
        else:
            self.tags = tags or []
        
        # Timestamps
        self.found_at = kwargs.get('found_at', datetime.utcnow().isoformat())
        self.posted_at = kwargs.get('posted_at')
        self.created_at = kwargs.get('created_at', datetime.utcnow().isoformat())
        self.updated_at = kwargs.get('updated_at', datetime.utcnow().isoformat())
    
    @classmethod
    def create_job(cls, user_id: str, website_id: str, job_data: Dict[str, Any]) -> Optional['Job']:
        """Create a new job record"""
        try:
            job_record = {
                'id': str(uuid.uuid4()),
                'user_id': user_id,
                'website_id': website_id,
                'title': job_data.get('title', ''),
                'company': job_data.get('company', ''),
                'location': job_data.get('location', ''),
                'salary_range': job_data.get('salary_range', ''),
                'job_url': job_data.get('job_url', ''),
                'description': job_data.get('description', ''),
                'requirements': job_data.get('requirements', ''),
                'job_type': job_data.get('job_type', ''),
                'remote_type': job_data.get('remote_type', ''),
                'external_id': job_data.get('external_id', ''),
                'tags': job_data.get('tags', []),
                'posted_at': job_data.get('posted_at')
            }
            
            return cls.create(job_record)
            
        except Exception as e:
            logger.error(f"Error creating job: {e}")
            return None
    
    @classmethod
    def create_from_scrape(cls, user_id: str, website_id: str, job_data: Dict[str, Any]) -> Optional['Job']:
        """Create job from scraped data"""
        return cls.create_job(user_id, website_id, job_data)
    
    def get_user(self):
        """Get the user who owns this job"""
        from .supabase_auth import User
        return User.get_by_id(self.user_id)
    
    def get_website(self):
        """Get the website this job came from"""
        from .supabase_website import JobWebsite
        if self.website_id:
            return JobWebsite.get_by_id(self.website_id)
        return None
    
    def get_applications(self):
        """Get applications for this job"""
        from .supabase_application import Application
        return Application.filter_by(job_id=self.id)
    
    def get_application_status(self) -> str:
        """Get application status for this job"""
        applications = self.get_applications()
        if applications:
            return applications[0].status
        return 'not_applied'
    
    @classmethod
    def get_by_user(cls, user_id: str) -> List['Job']:
        """Get all jobs for a user"""
        return cls.filter_by(user_id=user_id)
    
    @classmethod
    def get_by_website(cls, website_id: str) -> List['Job']:
        """Get all jobs from a website"""
        return cls.filter_by(website_id=website_id)
    
    @classmethod
    def search_jobs(cls, user_id: str, query: str = None, filters: Dict[str, Any] = None) -> List['Job']:
        """Search jobs with optional filters"""
        try:
            client = cls.get_client()
            query_builder = client.table(cls.table_name).select("*").eq('user_id', user_id)
            
            if filters:
                for key, value in filters.items():
                    if key in ['job_type', 'remote_type', 'website_id'] and value:
                        query_builder = query_builder.eq(key, value)
            
            if query:
                # Search in title, company, and description
                query_builder = query_builder.or_(
                    f"title.ilike.%{query}%,company.ilike.%{query}%,description.ilike.%{query}%"
                )
            
            result = query_builder.order('found_at', desc=True).execute()
            
            return [cls(**record) for record in result.data]
            
        except Exception as e:
            logger.error(f"Error searching jobs: {e}")
            return []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert job to dictionary"""
        result = super().to_dict()
        
        # Ensure JSON fields are properly serialized
        if isinstance(self.tags, list):
            result['tags'] = self.tags
        
        # Add related data
        website = self.get_website()
        result['website_name'] = website.name if website else None
        result['application_status'] = self.get_application_status()
        
        return result
    
    def update_tags(self, tags: List[str]) -> bool:
        """Update job tags"""
        try:
            return self.update({'tags': tags})
        except Exception as e:
            logger.error(f"Error updating job tags: {e}")
            return False
