from .database import db
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
import secrets

class User(UserMixin, db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Authentication
    email = db.Column(db.String(255), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    
    # Profile Information
    full_name = db.Column(db.String(255), nullable=True)
    phone = db.Column(db.String(50), nullable=True)
    location = db.Column(db.String(255), nullable=True)
    
    # Account Status
    is_active = db.Column(db.Boolean, default=True)
    email_verified = db.Column(db.Bo<PERSON>an, default=False)
    verification_token = db.Column(db.String(100), nullable=True)
    
    # Login tracking
    last_login = db.Column(db.DateTime, nullable=True)
    login_count = db.Column(db.Integer, default=0)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    resumes = db.relationship('Resume', backref='user', lazy=True, cascade='all, delete-orphan')
    jobs = db.relationship('Job', backref='user', lazy=True, cascade='all, delete-orphan')
    applications = db.relationship('Application', backref='user', lazy=True, cascade='all, delete-orphan')
    websites = db.relationship('JobWebsite', backref='user', lazy=True, cascade='all, delete-orphan')
    profile = db.relationship('UserProfile', backref='user', uselist=False, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<User {self.email}>'
    
    def set_password(self, password):
        """Set password hash"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check password"""
        return check_password_hash(self.password_hash, password)
    
    def generate_verification_token(self):
        """Generate email verification token"""
        self.verification_token = secrets.token_urlsafe(32)
        return self.verification_token
    
    def verify_email(self, token):
        """Verify email with token"""
        if self.verification_token == token:
            self.email_verified = True
            self.verification_token = None
            db.session.commit()
            return True
        return False
    
    def record_login(self):
        """Record user login"""
        self.last_login = datetime.utcnow()
        self.login_count += 1
        db.session.commit()
    
    def to_dict(self):
        """Convert user to dictionary"""
        return {
            'id': self.id,
            'email': self.email,
            'full_name': self.full_name,
            'phone': self.phone,
            'location': self.location,
            'is_active': self.is_active,
            'email_verified': self.email_verified,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'login_count': self.login_count,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def get_stats(self):
        """Get user statistics"""
        return {
            'total_resumes': len(self.resumes),
            'total_jobs': len(self.jobs),
            'total_applications': len(self.applications),
            'active_websites': len([w for w in self.websites if w.active]),
            'member_since': self.created_at.strftime('%B %Y') if self.created_at else 'Unknown'
        }

class UserProfile(db.Model):
    __tablename__ = 'user_profiles'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Extracted CV Information
    skills = db.Column(db.Text, nullable=True)
    experience_summary = db.Column(db.Text, nullable=True)
    preferred_job_titles = db.Column(db.Text, nullable=True)
    preferred_locations = db.Column(db.Text, nullable=True)
    
    # Career Information
    current_position = db.Column(db.String(255), nullable=True)
    current_company = db.Column(db.String(255), nullable=True)
    years_of_experience = db.Column(db.Integer, nullable=True)
    education_level = db.Column(db.String(100), nullable=True)
    
    # Job Search Preferences
    salary_expectation_min = db.Column(db.Integer, nullable=True)
    salary_expectation_max = db.Column(db.Integer, nullable=True)
    job_type_preference = db.Column(db.String(50), nullable=True)  # full-time, part-time, contract
    remote_preference = db.Column(db.String(50), nullable=True)  # remote, hybrid, onsite
    
    # Social Links
    linkedin_url = db.Column(db.String(500), nullable=True)
    github_url = db.Column(db.String(500), nullable=True)
    portfolio_url = db.Column(db.String(500), nullable=True)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<UserProfile {self.user.email}>'
    
    def to_dict(self):
        """Convert profile to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'skills': self.skills,
            'experience_summary': self.experience_summary,
            'preferred_job_titles': self.preferred_job_titles,
            'preferred_locations': self.preferred_locations,
            'current_position': self.current_position,
            'current_company': self.current_company,
            'years_of_experience': self.years_of_experience,
            'education_level': self.education_level,
            'salary_expectation_min': self.salary_expectation_min,
            'salary_expectation_max': self.salary_expectation_max,
            'job_type_preference': self.job_type_preference,
            'remote_preference': self.remote_preference,
            'linkedin_url': self.linkedin_url,
            'github_url': self.github_url,
            'portfolio_url': self.portfolio_url,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def update_from_cv_extraction(self, extracted_data):
        """Update profile from CV extraction data"""
        personal_info = extracted_data.get('personal_info', {})
        skills_data = extracted_data.get('skills', {})
        experience_data = extracted_data.get('experience', [])
        
        # Update skills
        if skills_data.get('technical'):
            self.skills = ', '.join(skills_data['technical'])
        
        # Update experience summary
        if experience_data:
            latest_job = experience_data[0] if experience_data else {}
            self.current_position = latest_job.get('position')
            self.current_company = latest_job.get('company')
            
            # Calculate years of experience
            total_experience = len(experience_data)
            self.years_of_experience = total_experience
        
        # Update social links
        self.linkedin_url = personal_info.get('linkedin')
        
        self.updated_at = datetime.utcnow()
        db.session.commit()
    
    @classmethod
    def create_for_user(cls, user_id):
        """Create profile for user"""
        profile = cls(user_id=user_id)
        db.session.add(profile)
        db.session.commit()
        return profile
