<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - AI Job Application Assistant</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">AI Job Application Assistant</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{{ url_for('upload') }}" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Upload</a>
                    <a href="{{ url_for('profile') }}" class="bg-blue-100 text-blue-700 px-3 py-2 rounded-md text-sm font-medium">Profile</a>
                    <a href="{{ url_for('websites') }}" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Websites</a>
                    <a href="{{ url_for('automate') }}" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Automate</a>
                    <a href="{{ url_for('jobs') }}" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Jobs</a>
                    <a href="{{ url_for('logout') }}" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Logout</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="mb-6">
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'success' if category == 'success' else 'danger' if category == 'error' else 'warning' if category == 'warning' else 'info' }} 
                                    bg-{{ 'green' if category == 'success' else 'red' if category == 'error' else 'yellow' if category == 'warning' else 'blue' }}-100 
                                    border border-{{ 'green' if category == 'success' else 'red' if category == 'error' else 'yellow' if category == 'warning' else 'blue' }}-400 
                                    text-{{ 'green' if category == 'success' else 'red' if category == 'error' else 'yellow' if category == 'warning' else 'blue' }}-700 
                                    px-4 py-3 rounded mb-4">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Profile Management</h1>
            <p class="mt-2 text-gray-600">Manage your account information and resume-based profile data</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Website Account Profile -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900 flex items-center">
                        <i class="fas fa-user-circle mr-2 text-blue-500"></i>
                        Website Account Profile
                    </h2>
                    <p class="text-sm text-gray-500 mt-1">Your login credentials and account settings</p>
                </div>
                <div class="px-6 py-4">
                    <form method="POST" action="{{ url_for('update_profile') }}" class="space-y-4">
                        <div>
                            <label for="account_email" class="block text-sm font-medium text-gray-700">Email (Login)</label>
                            <input type="email" id="account_email" value="{{ current_user.email }}" 
                                   class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-100" 
                                   readonly>
                            <p class="text-xs text-gray-500 mt-1">Email cannot be changed after registration</p>
                        </div>
                        
                        <div>
                            <label for="full_name" class="block text-sm font-medium text-gray-700">Full Name</label>
                            <input type="text" id="full_name" name="full_name" value="{{ user_data.full_name }}" 
                                   class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700">Phone Number</label>
                            <input type="tel" id="phone" name="phone" value="{{ user_data.phone }}" 
                                   class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <div>
                            <label for="location" class="block text-sm font-medium text-gray-700">Location</label>
                            <input type="text" id="location" name="location" value="{{ user_data.get('location', '') }}" 
                                   class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="e.g., San Francisco, CA">
                        </div>
                        
                        <div class="pt-4">
                            <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                Update Account Profile
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Resume-Based Profile -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900 flex items-center">
                        <i class="fas fa-file-alt mr-2 text-green-500"></i>
                        Resume-Based Profile
                        {% if resume_parsed %}
                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-check-circle mr-1"></i>
                                Parsed
                            </span>
                        {% else %}
                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                Not Parsed
                            </span>
                        {% endif %}
                    </h2>
                    <p class="text-sm text-gray-500 mt-1">Information extracted from your uploaded resume</p>
                </div>
                <div class="px-6 py-4">
                    {% if latest_resume %}
                        <div class="mb-4 p-3 bg-blue-50 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-blue-900">Latest Resume</p>
                                    <p class="text-xs text-blue-700">{{ latest_resume.filename }}</p>
                                    <p class="text-xs text-blue-600">
                                        Uploaded: {{ latest_resume.uploaded_at.strftime('%Y-%m-%d %H:%M') }}
                                    </p>
                                </div>
                                <div class="text-right">
                                    {% if latest_resume.parsing_status == 'completed' %}
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-check mr-1"></i>
                                            Parsed
                                        </span>
                                    {% elif latest_resume.parsing_status == 'processing' %}
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            <i class="fas fa-spinner fa-spin mr-1"></i>
                                            Processing
                                        </span>
                                    {% elif latest_resume.parsing_status == 'failed' %}
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="fas fa-times mr-1"></i>
                                            Failed
                                        </span>
                                    {% else %}
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            <i class="fas fa-clock mr-1"></i>
                                            Pending
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    <form method="POST" action="{{ url_for('update_profile') }}" class="space-y-4">
                        <div>
                            <label for="skills" class="block text-sm font-medium text-gray-700">Technical Skills</label>
                            <textarea id="skills" name="skills" rows="3" 
                                      class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
                                      placeholder="e.g., Python, JavaScript, React, AWS">{{ user_data.skills }}</textarea>
                            <p class="text-xs text-gray-500 mt-1">Comma-separated list of your technical skills</p>
                        </div>
                        
                        <div>
                            <label for="experience" class="block text-sm font-medium text-gray-700">Experience Summary</label>
                            <textarea id="experience" name="experience" rows="4" 
                                      class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
                                      placeholder="Brief summary of your professional experience">{{ user_data.experience }}</textarea>
                        </div>
                        
                        <div>
                            <label for="preferred_titles" class="block text-sm font-medium text-gray-700">Preferred Job Titles</label>
                            <input type="text" id="preferred_titles" name="preferred_titles" value="{{ user_data.preferred_titles }}" 
                                   class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
                                   placeholder="e.g., Software Engineer, Full Stack Developer">
                            <p class="text-xs text-gray-500 mt-1">Comma-separated list of job titles you're interested in</p>
                        </div>
                        
                        <div>
                            <label for="locations" class="block text-sm font-medium text-gray-700">Preferred Locations</label>
                            <input type="text" id="locations" name="locations" value="{{ user_data.locations }}" 
                                   class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
                                   placeholder="e.g., Remote, San Francisco, New York">
                            <p class="text-xs text-gray-500 mt-1">Comma-separated list of preferred work locations</p>
                        </div>
                        
                        <div class="pt-4">
                            <button type="submit" class="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                                Update Resume Profile
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Resume Upload Section -->
        <div class="mt-8 bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900 flex items-center">
                    <i class="fas fa-upload mr-2 text-purple-500"></i>
                    Resume Management
                </h2>
                <p class="text-sm text-gray-500 mt-1">Upload a new resume to update your profile automatically</p>
            </div>
            <div class="px-6 py-4">
                <div class="flex items-center justify-center w-full">
                    <a href="{{ url_for('upload') }}" 
                       class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
                        <div class="flex flex-col items-center justify-center pt-5 pb-6">
                            <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                            <p class="mb-2 text-sm text-gray-500">
                                <span class="font-semibold">Click to upload</span> a new resume
                            </p>
                            <p class="text-xs text-gray-500">PDF, DOC, DOCX (MAX. 16MB)</p>
                        </div>
                    </a>
                </div>
                
                {% if not resume_parsed %}
                    <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div class="flex">
                            <i class="fas fa-info-circle text-yellow-400 mt-0.5 mr-3"></i>
                            <div>
                                <h3 class="text-sm font-medium text-yellow-800">Resume Not Parsed</h3>
                                <p class="text-sm text-yellow-700 mt-1">
                                    Upload your resume to automatically populate your profile with extracted information like skills, experience, and job titles.
                                </p>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Profile Completion Status -->
        <div class="mt-8 bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900 flex items-center">
                    <i class="fas fa-chart-pie mr-2 text-indigo-500"></i>
                    Profile Completion
                </h2>
            </div>
            <div class="px-6 py-4">
                {% set completion_score = 0 %}
                {% if user_data.full_name %}{% set completion_score = completion_score + 15 %}{% endif %}
                {% if user_data.phone %}{% set completion_score = completion_score + 10 %}{% endif %}
                {% if user_data.get('location') %}{% set completion_score = completion_score + 10 %}{% endif %}
                {% if user_data.skills %}{% set completion_score = completion_score + 25 %}{% endif %}
                {% if user_data.experience %}{% set completion_score = completion_score + 20 %}{% endif %}
                {% if user_data.preferred_titles %}{% set completion_score = completion_score + 15 %}{% endif %}
                {% if user_data.locations %}{% set completion_score = completion_score + 5 %}{% endif %}
                
                <div class="flex items-center justify-between mb-4">
                    <span class="text-sm font-medium text-gray-700">Profile Completion</span>
                    <span class="text-sm font-medium text-gray-900">{{ completion_score }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-indigo-600 h-2 rounded-full" style="width: {{ completion_score }}%"></div>
                </div>
                
                <div class="mt-4 grid grid-cols-2 gap-4 text-sm">
                    <div class="flex items-center">
                        <i class="fas fa-{{ 'check text-green-500' if user_data.full_name else 'times text-red-500' }} mr-2"></i>
                        <span class="{{ 'text-green-700' if user_data.full_name else 'text-red-700' }}">Full Name</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-{{ 'check text-green-500' if user_data.phone else 'times text-red-500' }} mr-2"></i>
                        <span class="{{ 'text-green-700' if user_data.phone else 'text-red-700' }}">Phone Number</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-{{ 'check text-green-500' if user_data.get('location') else 'times text-red-500' }} mr-2"></i>
                        <span class="{{ 'text-green-700' if user_data.get('location') else 'text-red-700' }}">Location</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-{{ 'check text-green-500' if user_data.skills else 'times text-red-500' }} mr-2"></i>
                        <span class="{{ 'text-green-700' if user_data.skills else 'text-red-700' }}">Technical Skills</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-{{ 'check text-green-500' if user_data.experience else 'times text-red-500' }} mr-2"></i>
                        <span class="{{ 'text-green-700' if user_data.experience else 'text-red-700' }}">Experience</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-{{ 'check text-green-500' if user_data.preferred_titles else 'times text-red-500' }} mr-2"></i>
                        <span class="{{ 'text-green-700' if user_data.preferred_titles else 'text-red-700' }}">Preferred Titles</span>
                    </div>
                </div>
                
                {% if completion_score < 80 %}
                    <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                        <p class="text-sm text-blue-700">
                            <i class="fas fa-lightbulb mr-1"></i>
                            Complete your profile to improve job matching accuracy and automation success rate.
                        </p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-8 flex space-x-4">
            <a href="{{ url_for('websites') }}" 
               class="flex-1 bg-blue-600 text-white text-center py-3 px-4 rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                <i class="fas fa-globe mr-2"></i>
                Configure Job Websites
            </a>
            <a href="{{ url_for('automate') }}" 
               class="flex-1 bg-green-600 text-white text-center py-3 px-4 rounded-md hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                <i class="fas fa-robot mr-2"></i>
                Start Job Automation
            </a>
        </div>
    </div>

    <script>
        // Auto-refresh parsing status if resume is being processed
        {% if latest_resume and latest_resume.parsing_status == 'processing' %}
            setTimeout(function() {
                location.reload();
            }, 10000); // Refresh every 10 seconds
        {% endif %}
    </script>
</body>
</html>
