"""
Simple database setup script
"""

import sys
import os

# Add the parent directory to Python path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def setup_database():
    """Setup database with proper initialization"""
    
    print("🔧 Setting up Database")
    print("=" * 30)
    
    try:
        from flask import Flask
        from models.database import db, init_db
        from models.auth import User, UserProfile
        from models.website import JobWebsite
        
        app = Flask(__name__)
        
        # Initialize database
        database = init_db(app)
        
        with app.app_context():
            try:
                # Drop all tables and recreate (for clean setup)
                print("🗑️  Dropping existing tables...")
                db.drop_all()
                
                print("🏗️  Creating new tables...")
                db.create_all()
                
                print("✅ Database tables created successfully!")
                
                # Create admin user
                print("\n👤 Creating admin user...")
                admin = User(
                    email='<EMAIL>',
                    full_name='Admin User',
                    phone='******-0123',
                    location='San Francisco, CA',
                    is_active=True,
                    email_verified=True
                )
                admin.set_password('Admin123!')
                
                db.session.add(admin)
                db.session.commit()
                
                # Create profile
                profile = UserProfile(
                    user_id=admin.id,
                    skills='Python, Flask, JavaScript, React, SQL, Docker',
                    experience_summary='5+ years of full-stack development experience',
                    preferred_job_titles='Senior Developer, Tech Lead, Full Stack Engineer',
                    preferred_locations='Remote, San Francisco, New York',
                    current_position='Senior Software Engineer',
                    current_company='Tech Startup Inc.',
                    years_of_experience=5,
                    education_level='bachelor',
                    salary_expectation_min=80000,
                    salary_expectation_max=120000,
                    job_type_preference='full-time',
                    remote_preference='remote',
                    linkedin_url='https://linkedin.com/in/admin',
                    github_url='https://github.com/admin'
                )
                db.session.add(profile)
                db.session.commit()
                
                # Create default websites
                JobWebsite.create_defaults_for_user(admin.id)
                
                print("✅ Admin user created successfully!")
                print(f"   Email: <EMAIL>")
                print(f"   Password: Admin123!")
                
                # Create test user
                print("\n👤 Creating test user...")
                test_user = User(
                    email='<EMAIL>',
                    full_name='Test User',
                    phone='******-0456',
                    location='New York, NY',
                    is_active=True,
                    email_verified=True
                )
                test_user.set_password('Test123!')
                
                db.session.add(test_user)
                db.session.commit()
                
                # Create test profile
                test_profile = UserProfile(
                    user_id=test_user.id,
                    skills='Python, Data Analysis, Machine Learning',
                    experience_summary='3+ years in data science and analytics',
                    preferred_job_titles='Data Scientist, Python Developer',
                    preferred_locations='Remote, New York',
                    current_position='Data Analyst',
                    current_company='Analytics Corp',
                    years_of_experience=3,
                    education_level='master',
                    salary_expectation_min=60000,
                    salary_expectation_max=90000,
                    job_type_preference='full-time',
                    remote_preference='remote'
                )
                db.session.add(test_profile)
                db.session.commit()
                
                # Create default websites for test user
                JobWebsite.create_defaults_for_user(test_user.id)
                
                print("✅ Test user created successfully!")
                print(f"   Email: <EMAIL>")
                print(f"   Password: Test123!")
                
                print("\n📊 Database Statistics:")
                print(f"   Users: {User.query.count()}")
                print(f"   Profiles: {UserProfile.query.count()}")
                print(f"   Websites: {JobWebsite.query.count()}")
                
                print("\n🔐 Login credentials:")
                print("   Admin - Email: <EMAIL>, Password: Admin123!")
                print("   Test  - Email: <EMAIL>, Password: Test123!")
                
                print("\n🚀 Database setup complete! You can now run the app with: python app.py")
                
            except Exception as e:
                print(f"❌ Error setting up database: {e}")
                db.session.rollback()
                import traceback
                traceback.print_exc()
                
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running this from the project root directory")
        print("Current directory:", os.getcwd())
        print("Python path:", sys.path)

if __name__ == "__main__":
    setup_database()
