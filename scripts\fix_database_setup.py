"""
Fix database setup and create initial data
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask
from models import init_db, db
from models.auth import User, UserProfile
from models.website import JobWebsite

def setup_database():
    """Setup database with proper initialization"""
    
    print("🔧 Setting up Database")
    print("=" * 30)
    
    app = Flask(__name__)
    
    # Initialize database
    database = init_db(app)
    
    with app.app_context():
        try:
            # Drop all tables and recreate (for clean setup)
            print("🗑️  Dropping existing tables...")
            db.drop_all()
            
            print("🏗️  Creating new tables...")
            db.create_all()
            
            print("✅ Database tables created successfully!")
            
            # Create admin user
            print("\n👤 Creating admin user...")
            admin = User(
                email='<EMAIL>',
                full_name='Admin User',
                phone='******-0123',
                location='San Francisco, CA',
                is_active=True,
                email_verified=True
            )
            admin.set_password('Admin123!')
            
            db.session.add(admin)
            db.session.commit()
            
            # Create profile
            profile = UserProfile(
                user_id=admin.id,
                skills='Python, Flask, JavaScript, React, SQL, Docker',
                experience_summary='5+ years of full-stack development experience',
                preferred_job_titles='Senior Developer, Tech Lead, Full Stack Engineer',
                preferred_locations='Remote, San Francisco, New York',
                current_position='Senior Software Engineer',
                current_company='Tech Startup Inc.',
                years_of_experience=5,
                education_level='bachelor',
                salary_expectation_min=80000,
                salary_expectation_max=120000,
                job_type_preference='full-time',
                remote_preference='remote',
                linkedin_url='https://linkedin.com/in/admin',
                github_url='https://github.com/admin'
            )
            db.session.add(profile)
            db.session.commit()
            
            # Create default websites
            JobWebsite.create_defaults_for_user(admin.id)
            
            print("✅ Admin user created successfully!")
            print(f"   Email: {admin.email}")
            print(f"   Password: Admin123!")
            print(f"   Name: {admin.full_name}")
            print(f"   ID: {admin.id}")
            
            # Create test user
            print("\n👤 Creating test user...")
            test_user = User(
                email='<EMAIL>',
                full_name='Test User',
                phone='******-0456',
                location='New York, NY',
                is_active=True,
                email_verified=True
            )
            test_user.set_password('Test123!')
            
            db.session.add(test_user)
            db.session.commit()
            
            # Create test profile
            test_profile = UserProfile(
                user_id=test_user.id,
                skills='Python, Data Analysis, Machine Learning',
                experience_summary='3+ years in data science and analytics',
                preferred_job_titles='Data Scientist, Python Developer',
                preferred_locations='Remote, New York',
                current_position='Data Analyst',
                current_company='Analytics Corp',
                years_of_experience=3,
                education_level='master',
                salary_expectation_min=60000,
                salary_expectation_max=90000,
                job_type_preference='full-time',
                remote_preference='remote'
            )
            db.session.add(test_profile)
            db.session.commit()
            
            # Create default websites for test user
            JobWebsite.create_defaults_for_user(test_user.id)
            
            print("✅ Test user created successfully!")
            print(f"   Email: {test_user.email}")
            print(f"   Password: Test123!")
            
            print("\n📊 Database Statistics:")
            print(f"   Users: {User.query.count()}")
            print(f"   Profiles: {UserProfile.query.count()}")
            print(f"   Websites: {JobWebsite.query.count()}")
            
            print("\n🔐 Login credentials:")
            print("   Admin - Email: <EMAIL>, Password: Admin123!")
            print("   Test  - Email: <EMAIL>, Password: Test123!")
            
            print("\n🚀 Database setup complete! You can now run the app.")
            
        except Exception as e:
            print(f"❌ Error setting up database: {e}")
            db.session.rollback()

if __name__ == "__main__":
    setup_database()
