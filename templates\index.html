{% extends "base.html" %}

{% block title %}Upload Resume - Resume Job Automation{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-upload me-2"></i>Upload Your Resume</h4>
            </div>
            <div class="card-body">
                <p class="text-muted mb-4">
                    Upload your resume to get started with automated job applications. 
                    We'll extract your information and help you apply to relevant positions.
                </p>
                
                <form action="{{ url_for('upload_resume') }}" method="post" enctype="multipart/form-data">
                    <div class="mb-4">
                        <label for="resume" class="form-label">Select Resume File</label>
                        <input type="file" class="form-control" id="resume" name="resume" 
                               accept=".pdf,.doc,.docx,.txt" required>
                        <div class="form-text">
                            Supported formats: PDF, DOC, DOCX, TXT (Max size: 16MB)
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-cloud-upload-alt me-2"></i>Upload Resume
                        </button>
                    </div>
                </form>
                
                <hr class="my-4">
                
                <div class="text-center">
                    <p class="text-muted mb-3">Or skip upload and use demo data</p>
                    <a href="{{ url_for('edit_resume') }}" class="btn btn-outline-primary">
                        <i class="fas fa-play me-2"></i>Use Demo Data
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-body">
                <h5 class="card-title"><i class="fas fa-info-circle me-2"></i>How It Works</h5>
                <div class="row">
                    <div class="col-md-3 text-center mb-3">
                        <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                            <span class="fw-bold">1</span>
                        </div>
                        <p class="mt-2 small">Upload Resume</p>
                    </div>
                    <div class="col-md-3 text-center mb-3">
                        <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                            <span class="fw-bold">2</span>
                        </div>
                        <p class="mt-2 small">Edit Information</p>
                    </div>
                    <div class="col-md-3 text-center mb-3">
                        <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                            <span class="fw-bold">3</span>
                        </div>
                        <p class="mt-2 small">Auto Apply</p>
                    </div>
                    <div class="col-md-3 text-center mb-3">
                        <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                            <span class="fw-bold">4</span>
                        </div>
                        <p class="mt-2 small">Track Applications</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
