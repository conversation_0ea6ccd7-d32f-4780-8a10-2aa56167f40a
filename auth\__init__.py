from flask_login import LoginManager
from models.auth import User

login_manager = LoginManager()
login_manager.login_view = 'auth.login'
login_manager.login_message = 'Please log in to access this page.'
login_manager.login_message_category = 'info'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

def init_auth(app):
    """Initialize authentication"""
    login_manager.init_app(app)
    
    # Register auth blueprint
    from .routes import auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')
