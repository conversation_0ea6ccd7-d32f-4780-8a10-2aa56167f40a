{% extends "base.html" %}

{% block content %}
<div class="main-card">
    <div class="card-header">
        <h1 class="card-title">Job Search Automation</h1>
        <p class="card-subtitle">Let AI search and apply for jobs based on your preferences</p>
    </div>

    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 32px;">
        <!-- Job Preferences Panel -->
        <div style="background: #eff6ff; padding: 20px; border-radius: 8px; border: 1px solid #dbeafe;">
            <h3 style="display: flex; align-items: center; gap: 8px; font-size: 16px; font-weight: 600; color: #1e40af; margin-bottom: 16px;">
                <i class="fas fa-target"></i>
                Job Preferences
            </h3>
            
            <div style="margin-bottom: 12px;">
                <h4 style="font-weight: 500; color: #334155; margin-bottom: 4px; font-size: 14px;">Target Positions:</h4>
                <p style="color: #64748b; font-size: 14px;">{{ user_data.preferred_titles }}</p>
            </div>
            
            <div style="margin-bottom: 12px;">
                <h4 style="font-weight: 500; color: #334155; margin-bottom: 4px; font-size: 14px;">Locations:</h4>
                <p style="color: #64748b; font-size: 14px;">{{ user_data.locations }}</p>
            </div>
            
            <div>
                <h4 style="font-weight: 500; color: #334155; margin-bottom: 4px; font-size: 14px;">Key Skills:</h4>
                <p style="color: #64748b; font-size: 14px;">{{ user_data.skills }}</p>
            </div>
        </div>

        <!-- Target Websites Panel -->
        <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; border: 1px solid #bbf7d0;">
            <h3 style="display: flex; align-items: center; gap: 8px; font-size: 16px; font-weight: 600; color: #166534; margin-bottom: 16px;">
                <i class="fas fa-globe"></i>
                Target Websites
            </h3>
            
            {% for website in active_websites %}
            <div style="display: flex; align-items: center; justify-content: space-between; padding: 8px 0; {% if not loop.last %}border-bottom: 1px solid #dcfce7;{% endif %}">
                <span style="color: #334155; font-weight: 500; font-size: 14px;">{{ website.name }}</span>
                <span style="background: #16a34a; color: white; padding: 2px 8px; border-radius: 10px; font-size: 11px; font-weight: 500;">
                    Active
                </span>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Ready to Start Section -->
    <div class="text-center" style="padding: 24px 0;">
        <div style="width: 60px; height: 60px; background: #4f46e5; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 16px;">
            <i class="fas fa-briefcase" style="color: white; font-size: 24px;"></i>
        </div>
        
        <h2 style="font-size: 18px; font-weight: 600; color: #1e293b; margin-bottom: 8px;">Ready to Start</h2>
        <p style="color: #64748b; margin-bottom: 20px; max-width: 400px; margin-left: auto; margin-right: auto; font-size: 14px;">
            The automation will search {{ active_websites|length }} websites and apply to matching positions automatically.
        </p>
        
        <form action="{{ url_for('start_automation') }}" method="post">
            <button type="submit" class="btn btn-primary" style="padding: 12px 24px; font-size: 15px; font-weight: 600; background: #1e293b; color: white;" 
                    onclick="this.disabled=true; this.innerHTML='<i class=\'fas fa-spinner fa-spin\'></i> Starting...'; this.form.submit();">
                <i class="fas fa-play"></i>
                Start Job Automation
            </button>
        </form>
    </div>
</div>

<style>
@media (max-width: 768px) {
    .main-card > div:first-of-type {
        grid-template-columns: 1fr !important;
    }
}
</style>
{% endblock %}
