#!/usr/bin/env python3
"""
Test script for Supabase integration
"""
import os
import sys
import logging

# Add parent directory to path so we can import from models and utils
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_supabase_connection():
    """Test basic Supabase connection"""
    print("🔗 Testing Supabase connection...")
    
    try:
        from models.supabase_config import supabase_config
        
        if supabase_config.test_connection():
            print("✅ Supabase connection successful!")
            return True
        else:
            print("❌ Supabase connection failed!")
            return False
            
    except Exception as e:
        print(f"❌ Connection test error: {e}")
        return False

def test_supabase_models():
    """Test Supabase model operations"""
    print("\n📋 Testing Supabase models...")
    
    try:
        from models.supabase_auth import User, UserProfile
        from models.supabase_website import JobWebsite
        from models.supabase_resume import Resume
        from models.supabase_job import Job
        from models.supabase_application import Application
        from models.supabase_job_profile import JobProfile
        
        print("✅ All Supabase models imported successfully!")
        
        # Test basic model creation (without actually creating records)
        print("📝 Testing model instantiation...")
        
        user_data = {
            'email': '<EMAIL>',
            'full_name': 'Test User',
            'password_hash': 'test_hash'
        }
        user = User(**user_data)
        print(f"✅ User model: {user.email}")
        
        website_data = {
            'user_id': 'test-user-id',
            'name': 'Test Website',
            'url': 'https://example.com'
        }
        website = JobWebsite(**website_data)
        print(f"✅ JobWebsite model: {website.name}")
        
        resume_data = {
            'user_id': 'test-user-id',
            'filename': 'test_resume.pdf',
            'file_path': '/path/to/resume.pdf'
        }
        resume = Resume(**resume_data)
        print(f"✅ Resume model: {resume.filename}")
        
        job_data = {
            'user_id': 'test-user-id',
            'title': 'Test Job',
            'company': 'Test Company',
            'job_url': 'https://example.com/job'
        }
        job = Job(**job_data)
        print(f"✅ Job model: {job.title}")
        
        application_data = {
            'user_id': 'test-user-id',
            'job_id': 'test-job-id',
            'status': 'applied'
        }
        application = Application(**application_data)
        print(f"✅ Application model: {application.status}")
        
        profile_data = {
            'user_id': 'test-user-id',
            'full_name': 'Test User'
        }
        profile = JobProfile(**profile_data)
        print(f"✅ JobProfile model: {profile.full_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Model test error: {e}")
        return False

def test_database_operations():
    """Test basic database operations (read-only)"""
    print("\n🔍 Testing database operations...")
    
    try:
        from models.supabase_database import get_db_stats
        
        # Test getting database stats
        stats = get_db_stats()
        print(f"✅ Database stats retrieved: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database operations test error: {e}")
        return False

def test_migration_utility():
    """Test migration utility"""
    print("\n🔄 Testing migration utility...")
    
    try:
        from utils.supabase_migration import SupabaseMigration
        
        # Test migration utility initialization
        migration = SupabaseMigration()
        print("✅ Migration utility initialized")
        
        # Test SQLite check (should work even if no SQLite DB exists)
        migration.check_sqlite_exists()
        print("✅ SQLite check completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration utility test error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Supabase Integration Test Suite")
    print("=" * 50)
    
    # Set up logging
    logging.basicConfig(level=logging.WARNING)  # Reduce noise
    
    tests = [
        ("Connection Test", test_supabase_connection),
        ("Model Test", test_supabase_models),
        ("Database Operations Test", test_database_operations),
        ("Migration Utility Test", test_migration_utility)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Supabase integration is ready.")
        print("\n📋 Next steps:")
        print("1. Run the migration script: python scripts/migrate_to_supabase.py")
        print("2. Create tables in Supabase dashboard using the generated SQL")
        print("3. Test the new app: python app_supabase.py")
    else:
        print("⚠️  Some tests failed. Please check the configuration.")
        print("\n🔧 Troubleshooting:")
        print("1. Verify Supabase URL and anon key in models/supabase_config.py")
        print("2. Check internet connection")
        print("3. Ensure Supabase project is active")

if __name__ == "__main__":
    main()
