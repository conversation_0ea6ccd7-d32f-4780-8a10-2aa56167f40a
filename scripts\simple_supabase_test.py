#!/usr/bin/env python3
"""
Simple test for Supabase connection
"""
import sys
import os

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_basic_connection():
    """Test basic Supabase connection"""
    print("🔗 Testing basic Supabase connection...")
    
    try:
        from supabase import create_client
        
        url = "https://ngrnaolctyclsrvddgih.supabase.co"
        key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ncm5hb2xjdHljbHNydmRkZ2loIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA0MjYwMTksImV4cCI6MjA2NjAwMjAxOX0.d82N5jg0M3_PReIudCvKa5WVgDHXPsbcq0gmIl7aURU"
        
        client = create_client(url, key)
        print("✅ Supabase client created successfully!")
        
        # Try a simple query to test connection
        try:
            # This will fail if tables don't exist, but that's expected
            result = client.table('users').select('*').limit(1).execute()
            print(f"✅ Connection test successful! Found {len(result.data)} records")
        except Exception as e:
            if "relation \"users\" does not exist" in str(e) or "table" in str(e).lower():
                print("✅ Connection successful! (Tables not created yet - this is expected)")
            else:
                print(f"⚠️ Connection test warning: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

def show_sql_commands():
    """Show SQL commands to create tables"""
    print("\n📋 SQL Commands to run in Supabase Dashboard:")
    print("=" * 60)
    print("Go to: https://ngrnaolctyclsrvddgih.supabase.co/project/default/sql")
    print("Run these commands one by one:")
    print()
    
    sql_commands = [
        """-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255),
    phone VARCHAR(50),
    location VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    verification_token VARCHAR(100),
    last_login TIMESTAMPTZ,
    login_count INTEGER DEFAULT 0,
    skills TEXT,
    experience TEXT,
    preferred_titles TEXT,
    preferred_locations TEXT,
    resume_parsed BOOLEAN DEFAULT false,
    automation_status VARCHAR(50) DEFAULT 'idle',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);""",

        """-- Create user_profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    skills TEXT,
    experience_summary TEXT,
    preferred_job_titles TEXT,
    preferred_locations TEXT,
    current_position VARCHAR(255),
    current_company VARCHAR(255),
    years_of_experience INTEGER,
    education_level VARCHAR(100),
    salary_expectation_min INTEGER,
    salary_expectation_max INTEGER,
    job_type_preference VARCHAR(50),
    remote_preference VARCHAR(50),
    linkedin_url VARCHAR(500),
    github_url VARCHAR(500),
    portfolio_url VARCHAR(500),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);""",

        """-- Create job_websites table
CREATE TABLE IF NOT EXISTS job_websites (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    url VARCHAR(500) NOT NULL,
    icon VARCHAR(50) DEFAULT 'globe',
    active BOOLEAN DEFAULT true,
    last_scraped TIMESTAMPTZ,
    scrape_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);""",

        """-- Create resumes table
CREATE TABLE IF NOT EXISTS resumes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER,
    file_type VARCHAR(50),
    extracted_data JSONB DEFAULT '{}',
    extraction_status VARCHAR(50) DEFAULT 'pending',
    extraction_error TEXT,
    parsed_data JSONB DEFAULT '{}',
    parsing_status VARCHAR(50) DEFAULT 'pending',
    uploaded_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);""",

        """-- Create jobs table
CREATE TABLE IF NOT EXISTS jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    website_id UUID REFERENCES job_websites(id) ON DELETE SET NULL,
    title VARCHAR(255) NOT NULL,
    company VARCHAR(255) NOT NULL,
    location VARCHAR(255),
    salary_range VARCHAR(100),
    job_url VARCHAR(500) NOT NULL,
    description TEXT,
    requirements TEXT,
    job_type VARCHAR(50),
    remote_type VARCHAR(50),
    external_id VARCHAR(255),
    tags JSONB DEFAULT '[]',
    found_at TIMESTAMPTZ DEFAULT NOW(),
    posted_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);""",

        """-- Create applications table
CREATE TABLE IF NOT EXISTS applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    job_id UUID REFERENCES jobs(id) ON DELETE CASCADE,
    status VARCHAR(50) DEFAULT 'applied',
    cover_letter TEXT,
    notes TEXT,
    automation_log JSONB DEFAULT '[]',
    form_data JSONB DEFAULT '{}',
    applied_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);""",

        """-- Create job_profiles table
CREATE TABLE IF NOT EXISTS job_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    first_name VARCHAR(100),
    middle_name VARCHAR(100),
    last_name VARCHAR(100),
    full_name VARCHAR(255),
    date_of_birth DATE,
    nationality VARCHAR(100),
    gender VARCHAR(20),
    marital_status VARCHAR(50),
    email VARCHAR(255),
    phone_primary VARCHAR(50),
    phone_secondary VARCHAR(50),
    address_line1 VARCHAR(255),
    address_line2 VARCHAR(255),
    city VARCHAR(100),
    state_province VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100),
    professional_summary TEXT,
    career_objective TEXT,
    years_of_experience INTEGER,
    current_position VARCHAR(255),
    current_company VARCHAR(255),
    current_salary VARCHAR(100),
    expected_salary_min INTEGER,
    expected_salary_max INTEGER,
    salary_currency VARCHAR(10) DEFAULT 'USD',
    technical_skills TEXT,
    soft_skills TEXT,
    programming_languages TEXT,
    frameworks_libraries TEXT,
    databases TEXT,
    tools_software TEXT,
    operating_systems TEXT,
    cloud_platforms TEXT,
    methodologies TEXT,
    languages_spoken TEXT,
    work_experience TEXT,
    education TEXT,
    certifications TEXT,
    projects TEXT,
    publications TEXT,
    research_experience TEXT,
    awards TEXT,
    achievements TEXT,
    professional_memberships TEXT,
    volunteer_experience TEXT,
    references TEXT,
    preferred_job_titles TEXT,
    preferred_industries TEXT,
    preferred_company_sizes TEXT,
    preferred_locations TEXT,
    remote_work_preference VARCHAR(50),
    job_type_preference TEXT,
    availability_start_date DATE,
    willing_to_relocate BOOLEAN DEFAULT false,
    willing_to_travel VARCHAR(50),
    visa_status VARCHAR(100),
    work_authorization VARCHAR(255),
    linkedin_url VARCHAR(500),
    github_url VARCHAR(500),
    portfolio_url VARCHAR(500),
    personal_website VARCHAR(500),
    twitter_url VARCHAR(500),
    stackoverflow_url VARCHAR(500),
    behance_url VARCHAR(500),
    dribbble_url VARCHAR(500),
    hobbies_interests TEXT,
    additional_information TEXT,
    cover_letter_template TEXT,
    profile_completion_score REAL DEFAULT 0.0,
    last_updated_from_resume TIMESTAMPTZ,
    auto_update_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);"""
    ]
    
    for i, sql in enumerate(sql_commands, 1):
        print(f"-- Command {i}:")
        print(sql)
        print()

def main():
    """Main test function"""
    print("🧪 Simple Supabase Test")
    print("=" * 30)
    
    if test_basic_connection():
        print("\n✅ Basic connection test passed!")
        show_sql_commands()
        print("\n📋 Next Steps:")
        print("1. Run the SQL commands above in Supabase dashboard")
        print("2. Test the new app: python app_supabase.py")
        print("3. Or run migration: python scripts/migrate_to_supabase.py")
    else:
        print("\n❌ Connection test failed!")
        print("Please check your Supabase configuration.")

if __name__ == "__main__":
    main()
