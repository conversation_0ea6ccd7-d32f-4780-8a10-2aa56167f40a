#!/usr/bin/env python3
"""
Migration script to move from SQLite to Supabase
"""
import os
import sys
import logging

# Add parent directory to path so we can import from models and utils
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.supabase_migration import SupabaseMigration

def main():
    """Run the migration"""
    print("🚀 Supabase Migration Tool")
    print("=" * 50)
    
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    # Get SQLite database path
    default_db_path = os.path.join(os.path.dirname(__file__), '..', 'instance', 'jobapp.db')
    
    print(f"Default SQLite database path: {default_db_path}")
    
    # Ask user if they want to use default path or specify custom
    use_default = input("Use default SQLite database path? (y/n): ").lower().strip()
    
    if use_default == 'y':
        db_path = default_db_path
    else:
        db_path = input("Enter SQLite database path: ").strip()
    
    # Create migration instance
    migration = SupabaseMigration(db_path)
    
    print("\n📋 Migration Options:")
    print("1. Show SQL commands only (to create tables manually)")
    print("2. Run full migration (requires tables to be created first)")
    print("3. Test Supabase connection")
    
    choice = input("\nSelect option (1-3): ").strip()
    
    if choice == "1":
        print("\n📝 Generating SQL commands for Supabase...")
        migration.create_supabase_tables()
        
    elif choice == "2":
        print("\n🔄 Starting full migration...")
        print("⚠️  WARNING: This will migrate all data from SQLite to Supabase")
        print("⚠️  Make sure you have:")
        print("   1. Created a Supabase project")
        print("   2. Have the correct URL and anon key configured")
        print("   3. Backed up your SQLite database")
        
        confirm = input("\nContinue with migration? (yes/no): ").lower().strip()
        
        if confirm == "yes":
            success = migration.run_full_migration()
            
            # Save migration log
            log_file = migration.save_migration_log()
            
            if success:
                print("\n✅ Migration completed successfully!")
                print(f"📄 Migration log saved to: {log_file}")
            else:
                print("\n❌ Migration failed. Check the log for details.")
                print(f"📄 Migration log saved to: {log_file}")
        else:
            print("Migration cancelled.")
    
    elif choice == "3":
        print("\n🔗 Testing Supabase connection...")
        try:
            from models.supabase_config import supabase_config
            if supabase_config.test_connection():
                print("✅ Supabase connection successful!")
            else:
                print("❌ Supabase connection failed!")
        except Exception as e:
            print(f"❌ Connection test error: {e}")
    
    else:
        print("Invalid option selected.")
    
    print("\n🏁 Migration tool finished.")

if __name__ == "__main__":
    main()
