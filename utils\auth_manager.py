from flask_login import current_user
from models.auth import User, UserProfile
from models.website import JobWebsite
from models.job import Job
from models.application import Application
from models.resume import Resume
from models.database import db
from datetime import datetime

class AuthenticatedDatabaseManager:
    """Database manager for authenticated users"""
    
    @staticmethod
    def get_current_user():
        """Get current authenticated user"""
        if current_user.is_authenticated:
            return current_user
        return None
    
    @staticmethod
    def get_user_data():
        """Get user data for current user"""
        if not current_user.is_authenticated:
            return None
        
        profile = current_user.profile
        if not profile:
            profile = UserProfile.create_for_user(current_user.id)
        
        return {
            'full_name': current_user.full_name or '',
            'email': current_user.email,
            'phone': current_user.phone or '',
            'skills': profile.skills or '',
            'experience': profile.experience_summary or '',
            'preferred_titles': profile.preferred_job_titles or '',
            'locations': profile.preferred_locations or ''
        }
    
    @staticmethod
    def update_user_data(data):
        """Update user data for current user"""
        if not current_user.is_authenticated:
            return False
        
        try:
            # Update user basic info
            current_user.full_name = data.get('full_name', current_user.full_name)
            current_user.phone = data.get('phone', current_user.phone)
            
            # Get or create profile
            profile = current_user.profile
            if not profile:
                profile = UserProfile.create_for_user(current_user.id)
            
            # Update profile
            profile.skills = data.get('skills', profile.skills)
            profile.experience_summary = data.get('experience', profile.experience_summary)
            profile.preferred_job_titles = data.get('preferred_titles', profile.preferred_job_titles)
            profile.preferred_locations = data.get('locations', profile.preferred_locations)
            
            db.session.commit()
            return True
            
        except Exception as e:
            db.session.rollback()
            return False
    
    @staticmethod
    def get_job_websites():
        """Get job websites for current user"""
        if not current_user.is_authenticated:
            return []
        
        websites = JobWebsite.query.filter_by(user_id=current_user.id).all()
        
        # Create defaults if none exist
        if not websites:
            websites = JobWebsite.create_defaults_for_user(current_user.id)
        
        return [
            {
                'name': w.name,
                'url': w.url,
                'active': w.active,
                'icon': w.icon
            }
            for w in websites
        ]
    
    @staticmethod
    def toggle_website(website_name):
        """Toggle website for current user"""
        if not current_user.is_authenticated:
            return False
        
        website = JobWebsite.query.filter_by(
            user_id=current_user.id,
            name=website_name
        ).first()
        
        if website:
            return website.toggle_active()
        return False
    
    @staticmethod
    def add_website(name, url):
        """Add website for current user"""
        if not current_user.is_authenticated:
            return None
        
        website = JobWebsite(
            user_id=current_user.id,
            name=name,
            url=url,
            active=True
        )
        db.session.add(website)
        db.session.commit()
        return website
    
    @staticmethod
    def get_applied_jobs():
        """Get applied jobs for current user"""
        if not current_user.is_authenticated:
            return []
        
        applications = Application.query.filter_by(user_id=current_user.id)\
                                      .order_by(Application.applied_at.desc()).all()
        
        return [
            {
                'title': app.job.title if app.job else 'Unknown',
                'company': app.job.company if app.job else 'Unknown',
                'link': app.job.job_url if app.job else '#',
                'date': app.applied_at.strftime('%Y-%m-%d') if app.applied_at else '',
                'status': app.status.title(),
                'website': app.job.website.name if app.job and app.job.website else 'Unknown'
            }
            for app in applications
        ]
    
    @staticmethod
    def simulate_job_applications():
        """Create mock applications for current user"""
        if not current_user.is_authenticated:
            return []
        
        websites = JobWebsite.query.filter_by(user_id=current_user.id).all()
        if not websites:
            websites = JobWebsite.create_defaults_for_user(current_user.id)
        
        mock_jobs_data = [
            {
                'title': 'Senior Python Developer',
                'company': 'TechCorp Solutions',
                'job_url': 'https://remoteok.com/job/python-dev-123',
                'location': 'Remote',
                'description': 'Looking for experienced Python developer...',
                'website_name': 'RemoteOK'
            },
            {
                'title': 'Web Scraping Specialist',
                'company': 'Data Insights Ltd',
                'job_url': 'https://weworkremotely.com/job/scraper-456',
                'location': 'Remote',
                'description': 'Expert in web scraping and automation...',
                'website_name': 'We Work Remotely'
            },
            {
                'title': 'Flask Developer',
                'company': 'StartupXYZ',
                'job_url': 'https://angel.co/job/flask-789',
                'location': 'Remote',
                'description': 'Full-stack Flask developer needed...',
                'website_name': 'AngelList'
            }
        ]
        
        created_applications = []
        
        for job_data in mock_jobs_data:
            # Find matching website
            website = next((w for w in websites if w.name == job_data['website_name']), websites[0])
            
            # Create job
            job = Job.create_from_scrape(
                user_id=current_user.id,
                website_id=website.id,
                job_data=job_data
            )
            
            # Create application
            application = Application.create_from_automation(
                user_id=current_user.id,
                job_id=job.id,
                form_data={'automated': True}
            )
            
            created_applications.append(application)
        
        return created_applications
    
    @staticmethod
    def save_resume(filename, file_path, file_size, file_type):
        """Save resume for current user"""
        if not current_user.is_authenticated:
            return None
        
        resume = Resume(
            user_id=current_user.id,
            filename=filename,
            file_path=file_path,
            file_size=file_size,
            file_type=file_type,
            extraction_status='pending'
        )
        db.session.add(resume)
        db.session.commit()
        return resume
    
    @staticmethod
    def get_user_stats():
        """Get statistics for current user"""
        if not current_user.is_authenticated:
            return {}
        
        return {
            'total_resumes': Resume.query.filter_by(user_id=current_user.id).count(),
            'total_jobs': Job.query.filter_by(user_id=current_user.id).count(),
            'total_applications': Application.query.filter_by(user_id=current_user.id).count(),
            'active_websites': JobWebsite.query.filter_by(user_id=current_user.id, active=True).count()
        }
