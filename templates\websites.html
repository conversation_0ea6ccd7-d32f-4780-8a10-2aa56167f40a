{% extends "base.html" %}

{% block content %}
<div class="main-card">
    <div class="card-header">
        <h1 class="card-title">Configure Job Websites</h1>
        <p class="card-subtitle">Select and manage the job websites where applications will be submitted</p>
    </div>

    <div style="margin-bottom: 32px;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
            <h3 style="font-size: 18px; font-weight: 600; color: #374151;">Available Job Websites</h3>
            <span style="color: #6b7280; font-size: 14px;">
                <i class="fas fa-circle" style="color: #10b981; font-size: 8px;"></i>
                {{ websites|selectattr("active")|list|length }} Active
            </span>
        </div>
        <p style="color: #6b7280; margin-bottom: 24px;">Toggle websites on/off and manage your job sources</p>

        <div style="display: flex; flex-direction: column; gap: 16px;">
            {% for website in websites %}
            <div style="display: flex; align-items: center; justify-content: space-between; padding: 16px; border: 1px solid #e5e7eb; border-radius: 12px; background: {% if website.active %}#f0f9ff{% else %}#f9fafb{% endif %};">
                <div style="display: flex; align-items: center; gap: 12px;">
                    <div style="width: 40px; height: 40px; background: #4f46e5; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white;">
                        <i class="fas fa-{{ website.icon }}"></i>
                    </div>
                    <div>
                        <h4 style="font-weight: 600; color: #374151; margin-bottom: 4px;">{{ website.name }}</h4>
                        <p style="color: #6b7280; font-size: 14px;">{{ website.url }}</p>
                    </div>
                </div>
                <div style="display: flex; align-items: center; gap: 12px;">
                    <a href="{{ website.url }}" target="_blank" class="btn btn-secondary" style="padding: 8px 16px; font-size: 14px;">
                        <i class="fas fa-external-link-alt"></i>
                        Visit
                    </a>
                    <!-- Toggle Switch -->
                    <label class="toggle-switch" style="position: relative; display: inline-block; width: 44px; height: 24px; cursor: pointer;">
                        <input type="checkbox" {% if website.active %}checked{% endif %} 
                               onchange="toggleWebsite('{{ website.name }}')" 
                               style="opacity: 0; width: 0; height: 0; position: absolute;">
                        <span class="toggle-slider" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-color: {% if website.active %}#4f46e5{% else %}#cbd5e1{% endif %}; transition: all 0.3s ease; border-radius: 12px; overflow: hidden;">
                            <span class="toggle-knob" style="position: absolute; height: 18px; width: 18px; left: {% if website.active %}23px{% else %}3px{% endif %}; top: 3px; background-color: white; transition: all 0.3s ease; border-radius: 50%; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></span>
                        </span>
                    </label>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <div style="border-top: 1px solid #e5e7eb; padding-top: 32px; margin-bottom: 32px;">
        <h3 style="font-size: 18px; font-weight: 600; color: #374151; margin-bottom: 8px;">
            <i class="fas fa-plus"></i>
            Add New Job Website
        </h3>
        <p style="color: #6b7280; margin-bottom: 24px;">Add custom job websites to expand your search reach</p>

        <form action="{{ url_for('add_website') }}" method="post">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                <input type="text" name="website_name" placeholder="e.g., LinkedIn Jobs" 
                       style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 16px;" required>
                <input type="url" name="website_url" placeholder="e.g., https://linkedin.com/jobs" 
                       style="padding: 12px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 16px;" required>
            </div>
            <button type="submit" class="btn btn-primary" style="width: 100%; padding: 12px; font-size: 16px;">
                <i class="fas fa-plus"></i>
                Add Website
            </button>
        </form>
    </div>

    <div style="background: #f0f9ff; padding: 24px; border-radius: 12px; margin-bottom: 32px;">
        <h3 style="font-size: 18px; font-weight: 600; color: #374151; margin-bottom: 8px;">Ready for Automation</h3>
        <p style="color: #6b7280; margin-bottom: 16px;">The automation will search for jobs on {{ websites|selectattr("active")|list|length }} active websites:</p>
        
        <div style="display: flex; flex-wrap: wrap; gap: 8px; margin-bottom: 16px;">
            {% for website in websites %}
                {% if website.active %}
                <span style="background: #4f46e5; color: white; padding: 4px 12px; border-radius: 16px; font-size: 14px;">
                    {{ website.name }}
                </span>
                {% endif %}
            {% endfor %}
        </div>

        <a href="{{ url_for('automate') }}" class="btn btn-primary" style="padding: 12px 24px; font-size: 16px;">
            <i class="fas fa-arrow-right"></i>
            Continue to Automation
        </a>
    </div>
</div>

<script>
function toggleWebsite(websiteName) {
    fetch('/toggle-website', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({website: websiteName})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Smooth reload to update the UI
            setTimeout(() => {
                location.reload();
            }, 200);
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// Add CSS for toggle switch animation
const style = document.createElement('style');
style.textContent = `
    .toggle-switch {
        flex-shrink: 0;
    }
    
    .toggle-switch input:checked + .toggle-slider {
        background-color: #4f46e5 !important;
    }
    
    .toggle-switch input:checked + .toggle-slider .toggle-knob {
        transform: translateX(20px);
        left: 3px !important;
    }
    
    .toggle-slider {
        overflow: hidden !important;
        box-sizing: border-box;
    }
    
    .toggle-knob {
        transition: transform 0.3s ease !important;
        box-sizing: border-box;
    }
    
    .toggle-switch:hover .toggle-slider {
        box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
