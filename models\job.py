from .database import db
from datetime import datetime
from sqlalchemy.dialects.sqlite import JSON

class Job(db.Model):
    __tablename__ = 'jobs'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON>teger, db.<PERSON><PERSON>('users.id'), nullable=False)
    website_id = db.Column(db.Integer, db.ForeignKey('job_websites.id'), nullable=True)
    
    # Job information
    title = db.Column(db.String(255), nullable=False)
    company = db.Column(db.String(255), nullable=False)
    location = db.Column(db.String(255), nullable=True)
    salary_range = db.Column(db.String(100), nullable=True)
    job_url = db.Column(db.String(500), nullable=False)
    
    # Job details
    description = db.Column(db.Text, nullable=True)
    requirements = db.Column(db.Text, nullable=True)
    job_type = db.Column(db.String(50), nullable=True)  # full-time, part-time, contract
    remote_type = db.Column(db.String(50), nullable=True)  # remote, hybrid, onsite
    
    # Metadata
    external_id = db.Column(db.String(255), nullable=True)  # ID from job site
    tags = db.Column(JSON, nullable=True)  # Skills, technologies, etc.
    
    # Timestamps
    found_at = db.Column(db.DateTime, default=datetime.utcnow)
    posted_at = db.Column(db.DateTime, nullable=True)  # When job was posted on site
    
    # Relationships
    applications = db.relationship('Application', backref='job', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Job {self.title} at {self.company}>'
    
    def to_dict(self):
        """Convert job to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'website_id': self.website_id,
            'website_name': self.website.name if self.website else None,
            'title': self.title,
            'company': self.company,
            'location': self.location,
            'salary_range': self.salary_range,
            'job_url': self.job_url,
            'description': self.description,
            'requirements': self.requirements,
            'job_type': self.job_type,
            'remote_type': self.remote_type,
            'external_id': self.external_id,
            'tags': self.tags or [],
            'found_at': self.found_at.isoformat() if self.found_at else None,
            'posted_at': self.posted_at.isoformat() if self.posted_at else None,
            'application_status': self.get_application_status()
        }
    
    def get_application_status(self):
        """Get application status for this job"""
        application = Application.query.filter_by(job_id=self.id).first()
        return application.status if application else 'not_applied'
    
    @classmethod
    def create_from_scrape(cls, user_id, website_id, job_data):
        """Create job from scraped data"""
        job = cls(
            user_id=user_id,
            website_id=website_id,
            title=job_data.get('title', ''),
            company=job_data.get('company', ''),
            location=job_data.get('location'),
            salary_range=job_data.get('salary_range'),
            job_url=job_data.get('job_url', ''),
            description=job_data.get('description'),
            requirements=job_data.get('requirements'),
            job_type=job_data.get('job_type'),
            remote_type=job_data.get('remote_type'),
            external_id=job_data.get('external_id'),
            tags=job_data.get('tags', []),
            posted_at=job_data.get('posted_at')
        )
        
        db.session.add(job)
        db.session.commit()
        return job
    
    @classmethod
    def get_recent_for_user(cls, user_id, limit=50):
        """Get recent jobs for user"""
        return cls.query.filter_by(user_id=user_id)\
                       .order_by(cls.found_at.desc())\
                       .limit(limit).all()
