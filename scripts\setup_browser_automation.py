"""
Setup script for enhanced browser automation with browser-use
"""

import os
import sys
import subprocess
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_command(command, description):
    """Run a command and handle errors"""
    try:
        logger.info(f"🔧 {description}")
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        logger.info(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {description} failed: {e}")
        logger.error(f"Error output: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major == 3 and version.minor >= 11:
        logger.info(f"✅ Python {version.major}.{version.minor} is compatible")
        return True
    else:
        logger.error(f"❌ Python {version.major}.{version.minor} is not compatible. Requires Python 3.11+")
        return False

def setup_environment_file():
    """Create .env file with required variables"""
    env_content = """# Google API Key for Gemini (Required)
GOOGLE_API_KEY=your_google_api_key_here

# Optional: Browser automation settings
ANONYMIZED_TELEMETRY=false
BROWSER_USE_LOGGING_LEVEL=info

# Optional: Other AI providers
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
"""
    
    try:
        if not os.path.exists('.env'):
            with open('.env', 'w') as f:
                f.write(env_content)
            logger.info("✅ Created .env file with required variables")
        else:
            logger.info("ℹ️ .env file already exists")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to create .env file: {e}")
        return False

def main():
    """Main setup function"""
    logger.info("🚀 Setting up Enhanced Browser Automation with browser-use")
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Install required packages
    packages = [
        ("pip install --upgrade pip", "Upgrading pip"),
        ("pip install browser-use", "Installing browser-use"),
        ("pip install langchain-google-genai", "Installing Gemini integration"),
        ("pip install playwright", "Installing Playwright"),
        ("pip install PyPDF2", "Installing PDF parser"),
        ("pip install python-docx", "Installing DOCX parser"),
        ("pip install pydantic", "Installing Pydantic"),
        ("playwright install chromium --with-deps", "Installing Chromium browser"),
    ]
    
    failed_packages = []
    for command, description in packages:
        if not run_command(command, description):
            failed_packages.append(description)
    
    if failed_packages:
        logger.error(f"❌ Failed to install: {', '.join(failed_packages)}")
        return False
    
    # Setup environment file
    if not setup_environment_file():
        return False
    
    # Test imports
    try:
        logger.info("🧪 Testing imports...")
        import browser_use
        from langchain_google_genai import ChatGoogleGenerativeAI
        import PyPDF2
        import docx
        from pydantic import BaseModel
        logger.info("✅ All imports successful")
    except ImportError as e:
        logger.error(f"❌ Import test failed: {e}")
        return False
    
    # Final instructions
    logger.info("🎉 Setup completed successfully!")
    logger.info("")
    logger.info("📋 Next steps:")
    logger.info("1. Get your Google API key from: https://makersuite.google.com/app/apikey")
    logger.info("2. Add your API key to the .env file: GOOGLE_API_KEY=your_actual_key")
    logger.info("3. Run the application: python app.py")
    logger.info("")
    logger.info("🔧 Features enabled:")
    logger.info("- Real browser automation with anti-detection")
    logger.info("- Enhanced resume parsing")
    logger.info("- Gemini AI integration")
    logger.info("- Verification handling")
    logger.info("- Human-like behavior simulation")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
