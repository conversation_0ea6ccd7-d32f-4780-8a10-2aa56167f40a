{% extends "base.html" %}

{% block title %}Sign Up - AI Job Application Assistant{% endblock %}

{% block content %}
<div class="main-card" style="max-width: 400px; margin: 0 auto;">
    <div class="card-header">
        <h1 class="card-title">Create Account</h1>
        <p class="card-subtitle">Join us to automate your job applications</p>
    </div>

    <form action="{{ url_for('signup') }}" method="post">
        <div class="mb-16">
            <label class="form-label">Full Name</label>
            <input type="text" name="full_name" class="form-input" required 
                   value="{{ request.form.get('full_name', '') }}" 
                   placeholder="Enter your full name">
        </div>

        <div class="mb-16">
            <label class="form-label">Email Address</label>
            <input type="email" name="email" class="form-input" required 
                   value="{{ request.form.get('email', '') }}" 
                   placeholder="Enter your email">
        </div>

        <div class="mb-16">
            <label class="form-label">Password</label>
            <input type="password" name="password" class="form-input" required 
                   placeholder="Create a strong password">
            <div style="font-size: 12px; color: #64748b; margin-top: 4px;">
                Must be 8+ characters with uppercase, lowercase, and number
            </div>
        </div>

        <div class="mb-20">
            <label class="form-label">Confirm Password</label>
            <input type="password" name="confirm_password" class="form-input" required 
                   placeholder="Confirm your password">
        </div>

        <button type="submit" class="btn btn-primary" style="width: 100%; padding: 12px; font-size: 16px;">
            <i class="fas fa-user-plus"></i>
            Create Account
        </button>
    </form>

    <div class="text-center" style="margin-top: 24px; padding-top: 24px; border-top: 1px solid #e2e8f0;">
        <p style="color: #64748b; font-size: 14px; margin-bottom: 12px;">Already have an account?</p>
        <a href="{{ url_for('login') }}" class="btn btn-secondary">
            <i class="fas fa-sign-in-alt"></i>
            Sign In
        </a>
    </div>
</div>
{% endblock %}
