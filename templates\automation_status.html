{% extends "base.html" %}

{% block title %}Automation Status - AI Job Application Assistant{% endblock %}

{% block content %}
<div class="main-card">
    <div class="card-header">
        <h1 class="card-title">Job Automation Status</h1>
        <p class="card-subtitle">Real-time updates on your job search automation</p>
    </div>

    <div id="status-container">
        <!-- Status will be updated via JavaScript -->
        <div class="status-display">
            <div class="status-icon" id="status-icon">
                {% if automation_status == 'running' %}
                    <i class="fas fa-spinner fa-spin" style="color: #4f46e5; font-size: 32px;"></i>
                {% elif automation_status == 'completed' %}
                    <i class="fas fa-check-circle" style="color: #10b981; font-size: 32px;"></i>
                {% elif automation_status == 'error' %}
                    <i class="fas fa-exclamation-triangle" style="color: #ef4444; font-size: 32px;"></i>
                {% else %}
                    <i class="fas fa-clock" style="color: #6b7280; font-size: 32px;"></i>
                {% endif %}
            </div>
            
            <div class="status-text">
                <h3 id="status-title">
                    {% if automation_status == 'running' %}
                        Automation in Progress
                    {% elif automation_status == 'completed' %}
                        Automation Completed
                    {% elif automation_status == 'error' %}
                        Automation Error
                    {% else %}
                        Automation Ready
                    {% endif %}
                </h3>
                
                <p id="status-description">
                    {% if automation_status == 'running' %}
                        Searching for jobs and submitting applications...
                    {% elif automation_status == 'completed' %}
                        Job search completed successfully!
                    {% elif automation_status == 'error' %}
                        An error occurred during automation.
                    {% else %}
                        Ready to start job automation.
                    {% endif %}
                </p>
            </div>
        </div>

        <!-- Progress Stats -->
        <div class="progress-stats" style="margin-top: 32px;">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                <div class="stat-card">
                    <div class="stat-number" id="applications-count">{{ applications_count }}</div>
                    <div class="stat-label">Applications Found</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number" id="websites-scraped">0</div>
                    <div class="stat-label">Websites Scraped</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number">{{ recent_applications|length }}</div>
                    <div class="stat-label">Recent Applications</div>
                </div>
            </div>
        </div>

        <!-- Recent Applications -->
        {% if recent_applications %}
        <div style="margin-top: 32px;">
            <h3 style="font-size: 16px; font-weight: 600; color: #334155; margin-bottom: 16px;">Recent Applications</h3>
            
            <div style="display: flex; flex-direction: column; gap: 12px;">
                {% for app in recent_applications %}
                <div style="background: #f8fafc; padding: 16px; border-radius: 8px; border-left: 4px solid #4f46e5;">
                    <div style="display: flex; justify-content: between; align-items: start;">
                        <div style="flex: 1;">
                            <h4 style="font-weight: 600; color: #334155; margin-bottom: 4px;">{{ app.title }}</h4>
                            <p style="color: #64748b; font-size: 14px; margin-bottom: 4px;">{{ app.company }}</p>
                            <div style="display: flex; gap: 12px; font-size: 12px; color: #6b7280;">
                                <span>{{ app.website_name }}</span>
                                <span>•</span>
                                <span>{{ app.applied_at.strftime('%Y-%m-%d %H:%M') }}</span>
                                {% if app.match_score %}
                                <span>•</span>
                                <span>{{ app.match_score|round(1) }}% match</span>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div style="margin-left: 16px;">
                            <span style="background: {% if app.status == 'Applied' %}#d1fae5{% else %}#fef3c7{% endif %}; 
                                         color: {% if app.status == 'Applied' %}#065f46{% else %}#92400e{% endif %}; 
                                         padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">
                                {{ app.status }}
                            </span>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Action Buttons -->
        <div style="margin-top: 32px; text-align: center;">
            {% if automation_status == 'running' %}
                <button class="btn btn-secondary" disabled>
                    <i class="fas fa-spinner fa-spin"></i>
                    Automation Running...
                </button>
            {% elif automation_status == 'completed' %}
                <a href="{{ url_for('jobs') }}" class="btn btn-primary">
                    <i class="fas fa-list"></i>
                    View All Applications
                </a>
                <a href="{{ url_for('start_automation') }}" class="btn btn-secondary" style="margin-left: 12px;" onclick="return confirm('Start new automation session?')">
                    <i class="fas fa-redo"></i>
                    Run Again
                </a>
            {% else %}
                <a href="{{ url_for('automate') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-left"></i>
                    Back to Automation
                </a>
            {% endif %}
        </div>
    </div>
</div>

<style>
.status-display {
    text-align: center;
    padding: 40px 20px;
}

.status-icon {
    margin-bottom: 16px;
}

.status-text h3 {
    font-size: 20px;
    font-weight: 600;
    color: #334155;
    margin-bottom: 8px;
}

.status-text p {
    color: #64748b;
    font-size: 16px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    text-align: center;
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #4f46e5;
    margin-bottom: 4px;
}

.stat-label {
    color: #64748b;
    font-size: 14px;
    font-weight: 500;
}
</style>

<script>
// Auto-refresh status every 5 seconds if automation is running
function updateStatus() {
    fetch('/api/automation-status')
        .then(response => response.json())
        .then(data => {
            // Update applications count
            document.getElementById('applications-count').textContent = data.applications_count;
            document.getElementById('websites-scraped').textContent = data.websites_scraped;
            
            // Update status if changed
            const currentStatus = '{{ automation_status }}';
            if (data.status !== currentStatus) {
                location.reload(); // Reload page if status changed
            }
        })
        .catch(error => console.error('Error updating status:', error));
}

// Auto-refresh if automation is running
{% if automation_status == 'running' %}
setInterval(updateStatus, 5000); // Update every 5 seconds
{% endif %}
</script>
{% endblock %}
