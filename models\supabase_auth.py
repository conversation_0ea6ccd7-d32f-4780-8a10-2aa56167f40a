"""
Supabase-compatible User and UserProfile models
"""
from typing import Optional, Dict, Any
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
import uuid
import logging
from .supabase_database import SupabaseModel

logger = logging.getLogger(__name__)

class User(UserMixin, SupabaseModel):
    """User model for Supabase"""
    
    table_name = "users"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Set default values
        self.id = kwargs.get('id', str(uuid.uuid4()))
        self.email = kwargs.get('email', '')
        self.password_hash = kwargs.get('password_hash', '')
        self.full_name = kwargs.get('full_name', '')
        self.phone = kwargs.get('phone', '')
        self.location = kwargs.get('location', '')
        self.is_active = kwargs.get('is_active', True)
        self.email_verified = kwargs.get('email_verified', False)
        self.verification_token = kwargs.get('verification_token', '')
        self.last_login = kwargs.get('last_login')
        self.login_count = kwargs.get('login_count', 0)
        self.created_at = kwargs.get('created_at', datetime.utcnow().isoformat())
        self.updated_at = kwargs.get('updated_at', datetime.utcnow().isoformat())
        
        # Additional fields for job automation
        self.skills = kwargs.get('skills', '')
        self.experience = kwargs.get('experience', '')
        self.preferred_titles = kwargs.get('preferred_titles', '')
        self.preferred_locations = kwargs.get('preferred_locations', '')
        self.resume_parsed = kwargs.get('resume_parsed', False)
        self.automation_status = kwargs.get('automation_status', 'idle')
    
    def set_password(self, password: str):
        """Set password hash"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password: str) -> bool:
        """Check password against hash"""
        return check_password_hash(self.password_hash, password)
    
    def record_login(self) -> bool:
        """Record user login"""
        try:
            self.last_login = datetime.utcnow().isoformat()
            self.login_count = (self.login_count or 0) + 1
            return self.update({
                'last_login': self.last_login,
                'login_count': self.login_count
            })
        except Exception as e:
            logger.error(f"Error recording login for user {self.id}: {e}")
            return False
    
    @classmethod
    def create_user(cls, email: str, password: str, full_name: str = '') -> Optional['User']:
        """Create a new user"""
        try:
            user_data = {
                'id': str(uuid.uuid4()),
                'email': email.lower().strip(),
                'full_name': full_name.strip(),
                'is_active': True,
                'email_verified': False,
                'login_count': 0
            }
            
            user = cls(**user_data)
            user.set_password(password)
            user_data['password_hash'] = user.password_hash
            
            return cls.create(user_data)
            
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            return None
    
    @classmethod
    def get_by_email(cls, email: str) -> Optional['User']:
        """Get user by email"""
        try:
            result = cls.get_client().table(cls.table_name).select("*").eq('email', email.lower().strip()).execute()
            
            if result.data:
                return cls(**result.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error getting user by email: {e}")
            return None
    
    def get_resumes(self):
        """Get user's resumes"""
        from .supabase_resume import Resume
        return Resume.filter_by(user_id=self.id)
    
    def get_jobs(self):
        """Get user's jobs"""
        from .supabase_job import Job
        return Job.filter_by(user_id=self.id)
    
    def get_applications(self):
        """Get user's applications"""
        from .supabase_application import Application
        return Application.filter_by(user_id=self.id)
    
    def get_websites(self):
        """Get user's job websites"""
        from .supabase_website import JobWebsite
        return JobWebsite.filter_by(user_id=self.id)
    
    def get_profile(self):
        """Get user's profile"""
        return UserProfile.filter_by(user_id=self.id)

class UserProfile(SupabaseModel):
    """User profile model for Supabase"""
    
    table_name = "user_profiles"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Set default values
        self.id = kwargs.get('id', str(uuid.uuid4()))
        self.user_id = kwargs.get('user_id', '')
        self.skills = kwargs.get('skills', '')
        self.experience_summary = kwargs.get('experience_summary', '')
        self.preferred_job_titles = kwargs.get('preferred_job_titles', '')
        self.preferred_locations = kwargs.get('preferred_locations', '')
        self.current_position = kwargs.get('current_position', '')
        self.current_company = kwargs.get('current_company', '')
        self.years_of_experience = kwargs.get('years_of_experience')
        self.education_level = kwargs.get('education_level', '')
        self.salary_expectation_min = kwargs.get('salary_expectation_min')
        self.salary_expectation_max = kwargs.get('salary_expectation_max')
        self.job_type_preference = kwargs.get('job_type_preference', '')
        self.remote_preference = kwargs.get('remote_preference', '')
        self.linkedin_url = kwargs.get('linkedin_url', '')
        self.github_url = kwargs.get('github_url', '')
        self.portfolio_url = kwargs.get('portfolio_url', '')
        self.created_at = kwargs.get('created_at', datetime.utcnow().isoformat())
        self.updated_at = kwargs.get('updated_at', datetime.utcnow().isoformat())
    
    @classmethod
    def get_or_create_for_user(cls, user_id: str) -> 'UserProfile':
        """Get or create profile for user"""
        try:
            profiles = cls.filter_by(user_id=user_id)
            if profiles:
                return profiles[0]
            
            # Create new profile
            profile_data = {
                'id': str(uuid.uuid4()),
                'user_id': user_id
            }
            return cls.create(profile_data)
            
        except Exception as e:
            logger.error(f"Error getting/creating user profile: {e}")
            return None
    
    def get_user(self) -> Optional[User]:
        """Get the user for this profile"""
        return User.get_by_id(self.user_id)
