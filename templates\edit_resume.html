{% extends "base.html" %}

{% block title %}Edit Resume Data - Resume Job Automation{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0"><i class="fas fa-edit me-2"></i>Edit Resume Information</h4>
            </div>
            <div class="card-body">
                <p class="text-muted mb-4">
                    Review and edit your extracted resume information before starting the job automation process.
                </p>
                
                <form action="{{ url_for('update_resume') }}" method="post">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="full_name" class="form-label">Full Name</label>
                            <input type="text" class="form-control" id="full_name" name="full_name" 
                                   value="{{ user_data.full_name }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="{{ user_data.email }}" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="tel" class="form-control" id="phone" name="phone" 
                               value="{{ user_data.phone }}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="skills" class="form-label">Skills</label>
                        <textarea class="form-control" id="skills" name="skills" rows="2" 
                                  placeholder="e.g., Python, JavaScript, React, Node.js" required>{{ user_data.skills }}</textarea>
                        <div class="form-text">Separate skills with commas</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="experience" class="form-label">Experience Summary</label>
                        <textarea class="form-control" id="experience" name="experience" rows="3" 
                                  placeholder="Brief summary of your work experience" required>{{ user_data.experience }}</textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="preferred_titles" class="form-label">Preferred Job Titles</label>
                            <input type="text" class="form-control" id="preferred_titles" name="preferred_titles" 
                                   value="{{ user_data.preferred_titles }}" 
                                   placeholder="e.g., Software Developer, Python Engineer" required>
                            <div class="form-text">Separate titles with commas</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="locations" class="form-label">Preferred Locations</label>
                            <input type="text" class="form-control" id="locations" name="locations" 
                                   value="{{ user_data.locations }}" 
                                   placeholder="e.g., Remote, New York, London" required>
                            <div class="form-text">Separate locations with commas</div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('index') }}" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-arrow-left me-2"></i>Back
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-check me-2"></i>Save & Continue
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
