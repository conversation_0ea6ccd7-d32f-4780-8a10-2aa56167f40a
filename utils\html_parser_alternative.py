"""
Alternative HTML parsing using BeautifulSoup instead of lxml
Use this if lxml continues to cause issues
"""

from bs4 import BeautifulSoup
import requests
from typing import List, Dict

class JobScraper:
    """Alternative job scraper using BeautifulSoup instead of lxml"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def scrape_remoteok(self, search_terms: str) -> List[Dict]:
        """
        Scrape RemoteOK for jobs using BeautifulSoup
        """
        try:
            url = f"https://remoteok.io/remote-{search_terms.replace(' ', '-')}-jobs"
            response = self.session.get(url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            jobs = []
            
            # Find job listings (adjust selectors based on actual site structure)
            job_elements = soup.find_all('tr', class_='job')
            
            for job_elem in job_elements[:5]:  # Limit to 5 jobs
                try:
                    title_elem = job_elem.find('h2')
                    company_elem = job_elem.find('h3')
                    link_elem = job_elem.find('a')
                    
                    if title_elem and company_elem and link_elem:
                        job = {
                            'title': title_elem.get_text(strip=True),
                            'company': company_elem.get_text(strip=True),
                            'link': f"https://remoteok.io{link_elem.get('href', '')}",
                            'date': '2025-06-20',
                            'status': 'Found'
                        }
                        jobs.append(job)
                        
                except Exception as e:
                    print(f"Error parsing job element: {e}")
                    continue
            
            return jobs
            
        except Exception as e:
            print(f"Error scraping RemoteOK: {e}")
            return self._get_fallback_jobs()
    
    def _get_fallback_jobs(self) -> List[Dict]:
        """Fallback mock jobs if scraping fails"""
        return [
            {
                "title": "Python Developer",
                "company": "TechCorp Solutions", 
                "link": "https://remoteok.com/job/python-dev-123",
                "date": "2025-06-20",
                "status": "Applied"
            },
            {
                "title": "Web Scraping Specialist",
                "company": "Data Insights Ltd",
                "link": "https://remoteok.com/job/scraper-456", 
                "date": "2025-06-20",
                "status": "Applied"
            }
        ]

# Test the scraper
if __name__ == "__main__":
    scraper = JobScraper()
    jobs = scraper.scrape_remoteok("python developer")
    print(f"Found {len(jobs)} jobs:")
    for job in jobs:
        print(f"- {job['title']} at {job['company']}")
