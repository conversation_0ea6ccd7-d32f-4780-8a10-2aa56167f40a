"""
Quick start script - creates database and runs basic setup
"""

import sys
import os

# Add the parent directory to Python path
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def quick_start():
    """Quick start setup"""
    
    print("🚀 Quick Start Setup")
    print("=" * 25)
    
    # Step 1: Test imports
    print("Step 1: Testing imports...")
    try:
        from flask import Flask
        from models.database import db, init_db
        from models.auth import User, UserProfile
        from models.website import JobWebsite
        print("✅ All imports successful")
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False
    
    # Step 2: Create directories
    print("\nStep 2: Creating directories...")
    directories = ['uploads', 'instance', 'auth']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ Created: {directory}/")
    
    # Step 3: Setup database
    print("\nStep 3: Setting up database...")
    try:
        app = Flask(__name__)
        init_db(app)
        
        with app.app_context():
            # Create tables
            db.create_all()
            
            # Check if admin exists
            admin = User.query.filter_by(email='<EMAIL>').first()
            if not admin:
                # Create admin
                admin = User(
                    email='<EMAIL>',
                    full_name='Admin User',
                    is_active=True,
                    email_verified=True
                )
                admin.set_password('Admin123!')
                db.session.add(admin)
                db.session.commit()
                
                # Create profile
                profile = UserProfile.create_for_user(admin.id)
                profile.skills = 'Python, Flask, JavaScript'
                profile.preferred_job_titles = 'Developer, Engineer'
                db.session.commit()
                
                # Create websites
                JobWebsite.create_defaults_for_user(admin.id)
                
                print("✅ Admin user created")
            else:
                print("✅ Admin user already exists")
        
        print("✅ Database setup complete")
        
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Step 4: Success message
    print("\n🎉 Setup Complete!")
    print("=" * 20)
    print("Login credentials:")
    print("  Email: <EMAIL>")
    print("  Password: Admin123!")
    print("\nTo start the app:")
    print("  python app.py")
    print("\nThen visit: http://localhost:5000")
    
    return True

if __name__ == "__main__":
    quick_start()
