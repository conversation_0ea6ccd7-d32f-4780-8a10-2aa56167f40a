"""
Supabase-compatible Application model
"""
from typing import Optional, Dict, Any, List
from datetime import datetime
import uuid
import json
import logging
from .supabase_database import SupabaseModel

logger = logging.getLogger(__name__)

class Application(SupabaseModel):
    """Application model for Supabase"""
    
    table_name = "applications"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Set default values
        self.id = kwargs.get('id', str(uuid.uuid4()))
        self.user_id = kwargs.get('user_id', '')
        self.job_id = kwargs.get('job_id', '')
        
        # Application status
        self.status = kwargs.get('status', 'applied')
        
        # Application details
        self.cover_letter = kwargs.get('cover_letter', '')
        self.notes = kwargs.get('notes', '')
        
        # Handle JSON fields
        automation_log = kwargs.get('automation_log')
        if isinstance(automation_log, str):
            try:
                self.automation_log = json.loads(automation_log)
            except json.JSONDecodeError:
                self.automation_log = []
        else:
            self.automation_log = automation_log or []
        
        form_data = kwargs.get('form_data')
        if isinstance(form_data, str):
            try:
                self.form_data = json.loads(form_data)
            except json.JSONDecodeError:
                self.form_data = {}
        else:
            self.form_data = form_data or {}
        
        # Timestamps
        self.applied_at = kwargs.get('applied_at', datetime.utcnow().isoformat())
        self.created_at = kwargs.get('created_at', datetime.utcnow().isoformat())
        self.updated_at = kwargs.get('updated_at', datetime.utcnow().isoformat())
    
    @classmethod
    def create_application(cls, user_id: str, job_id: str, 
                          status: str = 'applied', cover_letter: str = '',
                          notes: str = '', form_data: Dict[str, Any] = None,
                          automation_log: List[Dict[str, Any]] = None) -> Optional['Application']:
        """Create a new application record"""
        try:
            application_data = {
                'id': str(uuid.uuid4()),
                'user_id': user_id,
                'job_id': job_id,
                'status': status,
                'cover_letter': cover_letter,
                'notes': notes,
                'form_data': form_data or {},
                'automation_log': automation_log or []
            }
            
            return cls.create(application_data)
            
        except Exception as e:
            logger.error(f"Error creating application: {e}")
            return None
    
    @classmethod
    def create_from_automation(cls, user_id: str, job_id: str, 
                              form_data: Dict[str, Any] = None,
                              automation_log: List[Dict[str, Any]] = None) -> Optional['Application']:
        """Create application from automation data"""
        return cls.create_application(
            user_id=user_id,
            job_id=job_id,
            status='applied',
            form_data=form_data,
            automation_log=automation_log
        )
    
    def update_status(self, status: str, notes: str = '') -> bool:
        """Update application status"""
        try:
            update_data = {'status': status}
            if notes:
                update_data['notes'] = notes
            
            return self.update(update_data)
            
        except Exception as e:
            logger.error(f"Error updating application status: {e}")
            return False
    
    def add_automation_log(self, log_entry: Dict[str, Any]) -> bool:
        """Add entry to automation log"""
        try:
            current_log = self.automation_log or []
            current_log.append({
                **log_entry,
                'timestamp': datetime.utcnow().isoformat()
            })
            
            return self.update({'automation_log': current_log})
            
        except Exception as e:
            logger.error(f"Error adding automation log: {e}")
            return False
    
    def update_form_data(self, form_data: Dict[str, Any]) -> bool:
        """Update form data"""
        try:
            current_data = self.form_data or {}
            current_data.update(form_data)
            
            return self.update({'form_data': current_data})
            
        except Exception as e:
            logger.error(f"Error updating form data: {e}")
            return False
    
    def get_user(self):
        """Get the user who made this application"""
        from .supabase_auth import User
        return User.get_by_id(self.user_id)
    
    def get_job(self):
        """Get the job this application is for"""
        from .supabase_job import Job
        return Job.get_by_id(self.job_id)
    
    @classmethod
    def get_by_user(cls, user_id: str) -> List['Application']:
        """Get all applications for a user"""
        return cls.filter_by(user_id=user_id)
    
    @classmethod
    def get_by_job(cls, job_id: str) -> List['Application']:
        """Get all applications for a job"""
        return cls.filter_by(job_id=job_id)
    
    @classmethod
    def get_by_status(cls, user_id: str, status: str) -> List['Application']:
        """Get applications by status for a user"""
        try:
            client = cls.get_client()
            result = client.table(cls.table_name).select("*").eq('user_id', user_id).eq('status', status).execute()
            
            return [cls(**record) for record in result.data]
            
        except Exception as e:
            logger.error(f"Error getting applications by status: {e}")
            return []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert application to dictionary"""
        result = super().to_dict()
        
        # Ensure JSON fields are properly serialized
        if isinstance(self.automation_log, list):
            result['automation_log'] = self.automation_log
        if isinstance(self.form_data, dict):
            result['form_data'] = self.form_data
        
        # Add related data
        job = self.get_job()
        if job:
            result['job_title'] = job.title
            result['job_company'] = job.company
            result['job_url'] = job.job_url
        
        return result
    
    def get_status_display(self) -> str:
        """Get human-readable status"""
        status_map = {
            'applied': 'Applied',
            'pending': 'Pending Review',
            'rejected': 'Rejected',
            'interview': 'Interview Scheduled',
            'offer': 'Offer Received'
        }
        return status_map.get(self.status, self.status.title())
    
    def is_successful(self) -> bool:
        """Check if application was successful"""
        return self.status in ['interview', 'offer']
