from .database import db
from datetime import datetime
from sqlalchemy.dialects.sqlite import JSON

class Resume(db.Model):
    __tablename__ = 'resumes'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('users.id'), nullable=False)
    
    # File information
    filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer, nullable=True)
    file_type = db.Column(db.String(50), nullable=True)
    
    # Extraction data
    extracted_data = db.Column(JSON, nullable=True)
    extraction_status = db.Column(db.String(50), default='pending')  # pending, processing, completed, failed
    extraction_error = db.Column(db.Text, nullable=True)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<Resume {self.filename}>'
    
    def to_dict(self):
        """Convert resume to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'filename': self.filename,
            'file_path': self.file_path,
            'file_size': self.file_size,
            'file_type': self.file_type,
            'extracted_data': self.extracted_data or {},
            'extraction_status': self.extraction_status,
            'extraction_error': self.extraction_error,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def update_extraction_data(self, data, status='completed'):
        """Update extraction data"""
        self.extracted_data = data
        self.extraction_status = status
        self.updated_at = datetime.utcnow()
        db.session.commit()
    
    def mark_extraction_failed(self, error_message):
        """Mark extraction as failed"""
        self.extraction_status = 'failed'
        self.extraction_error = error_message
        self.updated_at = datetime.utcnow()
        db.session.commit()
    
    @property
    def is_processed(self):
        """Check if resume has been processed"""
        return self.extraction_status == 'completed'
    
    @property
    def personal_info(self):
        """Get personal info from extracted data"""
        if self.extracted_data:
            return self.extracted_data.get('personal_info', {})
        return {}
    
    @property
    def skills(self):
        """Get skills from extracted data"""
        if self.extracted_data:
            return self.extracted_data.get('skills', {})
        return {}
    
    @property
    def experience(self):
        """Get experience from extracted data"""
        if self.extracted_data:
            return self.extracted_data.get('experience', [])
        return []
