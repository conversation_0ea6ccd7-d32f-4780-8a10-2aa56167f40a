"""
Supabase-compatible Resume model
"""
from typing import Optional, Dict, Any, List
from datetime import datetime
import uuid
import json
import logging
from .supabase_database import SupabaseModel

logger = logging.getLogger(__name__)

class Resume(SupabaseModel):
    """Resume model for Supabase"""
    
    table_name = "resumes"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Set default values
        self.id = kwargs.get('id', str(uuid.uuid4()))
        self.user_id = kwargs.get('user_id', '')
        self.filename = kwargs.get('filename', '')
        self.file_path = kwargs.get('file_path', '')
        self.file_size = kwargs.get('file_size')
        self.file_type = kwargs.get('file_type', '')
        
        # Handle JSON fields
        extracted_data = kwargs.get('extracted_data')
        if isinstance(extracted_data, str):
            try:
                self.extracted_data = json.loads(extracted_data)
            except json.JSONDecodeError:
                self.extracted_data = {}
        else:
            self.extracted_data = extracted_data or {}
        
        self.extraction_status = kwargs.get('extraction_status', 'pending')
        self.extraction_error = kwargs.get('extraction_error', '')
        
        # Handle parsed_data (legacy field)
        parsed_data = kwargs.get('parsed_data')
        if isinstance(parsed_data, str):
            try:
                self.parsed_data = json.loads(parsed_data)
            except json.JSONDecodeError:
                self.parsed_data = {}
        else:
            self.parsed_data = parsed_data or {}
        
        self.parsing_status = kwargs.get('parsing_status', 'pending')
        self.uploaded_at = kwargs.get('uploaded_at', datetime.utcnow().isoformat())
        self.created_at = kwargs.get('created_at', datetime.utcnow().isoformat())
        self.updated_at = kwargs.get('updated_at', datetime.utcnow().isoformat())
    
    @classmethod
    def create_resume(cls, user_id: str, filename: str, file_path: str, 
                     file_size: int = None, file_type: str = '') -> Optional['Resume']:
        """Create a new resume record"""
        try:
            resume_data = {
                'id': str(uuid.uuid4()),
                'user_id': user_id,
                'filename': filename,
                'file_path': file_path,
                'file_size': file_size,
                'file_type': file_type,
                'extraction_status': 'pending',
                'parsing_status': 'pending',
                'extracted_data': {},
                'parsed_data': {}
            }
            
            return cls.create(resume_data)
            
        except Exception as e:
            logger.error(f"Error creating resume: {e}")
            return None
    
    def update_extraction_status(self, status: str, error: str = None) -> bool:
        """Update extraction status"""
        try:
            update_data = {'extraction_status': status}
            if error:
                update_data['extraction_error'] = error
            
            return self.update(update_data)
            
        except Exception as e:
            logger.error(f"Error updating extraction status: {e}")
            return False
    
    def update_parsing_status(self, status: str) -> bool:
        """Update parsing status"""
        try:
            return self.update({'parsing_status': status})
            
        except Exception as e:
            logger.error(f"Error updating parsing status: {e}")
            return False
    
    def save_extracted_data(self, data: Dict[str, Any]) -> bool:
        """Save extracted resume data"""
        try:
            update_data = {
                'extracted_data': data,
                'extraction_status': 'completed'
            }
            
            return self.update(update_data)
            
        except Exception as e:
            logger.error(f"Error saving extracted data: {e}")
            return False
    
    def save_parsed_data(self, data: Dict[str, Any]) -> bool:
        """Save parsed resume data (legacy support)"""
        try:
            update_data = {
                'parsed_data': data,
                'parsing_status': 'completed'
            }
            
            return self.update(update_data)
            
        except Exception as e:
            logger.error(f"Error saving parsed data: {e}")
            return False
    
    def get_user(self):
        """Get the user who owns this resume"""
        from .supabase_auth import User
        return User.get_by_id(self.user_id)
    
    @classmethod
    def get_by_user(cls, user_id: str) -> List['Resume']:
        """Get all resumes for a user"""
        return cls.filter_by(user_id=user_id)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert resume to dictionary"""
        result = super().to_dict()
        
        # Ensure JSON fields are properly serialized
        if isinstance(self.extracted_data, dict):
            result['extracted_data'] = self.extracted_data
        if isinstance(self.parsed_data, dict):
            result['parsed_data'] = self.parsed_data
            
        return result
    
    def get_display_name(self) -> str:
        """Get display name for resume"""
        if self.filename:
            return self.filename
        return f"Resume {self.id[:8]}"
    
    def is_processing(self) -> bool:
        """Check if resume is currently being processed"""
        return (self.extraction_status == 'processing' or 
                self.parsing_status == 'processing')
    
    def is_completed(self) -> bool:
        """Check if resume processing is completed"""
        return (self.extraction_status == 'completed' or 
                self.parsing_status == 'completed')
    
    def has_error(self) -> bool:
        """Check if resume processing has errors"""
        return (self.extraction_status == 'failed' or 
                self.parsing_status == 'failed')
