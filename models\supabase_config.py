"""
Supabase configuration and connection management
"""
import os
from supabase import create_client, Client
from typing import Optional
import logging

logger = logging.getLogger(__name__)

class SupabaseConfig:
    """Supabase configuration and client management"""
    
    def __init__(self):
        self.url: str = "https://ngrnaolctyclsrvddgih.supabase.co"
        self.anon_key: str = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ncm5hb2xjdHljbHNydmRkZ2loIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA0MjYwMTksImV4cCI6MjA2NjAwMjAxOX0.d82N5jg0M3_PReIudCvKa5WVgDHXPsbcq0gmIl7aURU"
        self._client: Optional[Client] = None
    
    @property
    def client(self) -> Client:
        """Get or create Supabase client"""
        if self._client is None:
            try:
                self._client = create_client(self.url, self.anon_key)
                logger.info("✅ Supabase client initialized")
            except Exception as e:
                logger.error(f"❌ Failed to create Supabase client: {e}")
                # Create a minimal client for testing
                self._client = create_client(self.url, self.anon_key)
        return self._client
    
    def test_connection(self) -> bool:
        """Test Supabase connection"""
        try:
            # Try to fetch from a system table to test connection
            result = self.client.table('users').select('id').limit(1).execute()
            logger.info("✅ Supabase connection test successful")
            return True
        except Exception as e:
            logger.error(f"❌ Supabase connection test failed: {e}")
            return False
    
    def create_tables(self) -> bool:
        """Create database tables in Supabase"""
        try:
            # Note: In Supabase, we typically create tables via SQL in the dashboard
            # or using migrations. This method can be used for programmatic table creation
            # if needed, but it's recommended to use Supabase's migration system.
            
            # For now, we'll assume tables are created via Supabase dashboard
            # and this method will just verify they exist
            
            tables_to_check = [
                'users', 'user_profiles', 'resumes', 'jobs', 
                'applications', 'job_websites', 'job_profiles'
            ]
            
            for table in tables_to_check:
                try:
                    self.client.table(table).select('*').limit(1).execute()
                    logger.info(f"✅ Table '{table}' exists")
                except Exception as e:
                    logger.warning(f"⚠️ Table '{table}' may not exist: {e}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error checking tables: {e}")
            return False

# Global Supabase instance
supabase_config = SupabaseConfig()

def get_supabase_client() -> Client:
    """Get the global Supabase client"""
    return supabase_config.client

def init_supabase(app=None) -> Client:
    """Initialize Supabase for Flask app"""
    client = get_supabase_client()
    
    if app:
        # Store client in app config for access in routes
        app.config['SUPABASE_CLIENT'] = client
        logger.info("✅ Supabase initialized with Flask app")
    
    return client
